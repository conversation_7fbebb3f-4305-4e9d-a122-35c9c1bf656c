defmodule StarlightWeb.MapLive do
  @moduledoc """
  Main map interface LiveView for bridge engineering analysis.

  This LiveView provides the primary interface for:
  - Viewing bridges on a map
  - Drawing routes
  - Managing bridge and vehicle data
  - Running analysis
  """

  use StarlightWeb, :live_view

  alias Starlight.{Bridges, Vehicles, Routes, Analysis}
  alias StarlightWeb.MapLive.{BridgeFormComponent, VehicleFormComponent, AnalysisResultsComponent}

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # Subscribe to real-time updates
      Phoenix.PubSub.subscribe(Starlight.PubSub, "bridges")
      Phoenix.PubSub.subscribe(Starlight.PubSub, "routes")
      Phoenix.PubSub.subscribe(Starlight.PubSub, "analysis")
    end

    socket =
      socket
      |> assign(:page_title, "Bridge Analysis Map")
      |> assign(:bridges, [])
      |> assign(:routes, [])
      |> assign(:selected_bridge, nil)
      |> assign(:selected_route, nil)
      |> assign(:selected_vehicle, nil)
      |> assign(:analysis_results, [])
      |> assign(:show_bridge_form, false)
      |> assign(:show_vehicle_form, false)
      |> assign(:show_analysis_results, false)
      |> assign(:current_user, nil)  # Will be set from session
      |> load_initial_data()

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Bridge Analysis Map")
  end

  defp apply_action(socket, :bridge, %{"id" => id}) do
    bridge = Bridges.get_bridge!(id)

    socket =
      socket
      |> assign(:page_title, "Bridge: #{bridge.name}")
      |> assign(:selected_bridge, bridge)

    # Ensure bridges are loaded before highlighting
    if connected?(socket) do
      # Send highlight event after a small delay to ensure map is ready
      Process.send_after(self(), {:highlight_bridge, bridge}, 100)
    end

    socket
  end

  defp apply_action(socket, :route, %{"id" => id}) do
    route = Routes.get_route!(id)

    socket
    |> assign(:page_title, "Route: #{route.name}")
    |> assign(:selected_route, route)
  end

  @impl true
  def handle_event("request_bridges", _params, socket) do
    bridges = Bridges.list_bridges()

    # Convert bridges to map-friendly format
    bridge_data = Enum.map(bridges, fn bridge ->
      %{
        id: bridge.id,
        name: bridge.name,
        latitude: bridge.latitude && Decimal.to_float(bridge.latitude),
        longitude: bridge.longitude && Decimal.to_float(bridge.longitude),
        condition_rating: bridge.condition_rating,
        load_rating: bridge.load_rating && Decimal.to_float(bridge.load_rating),
        span_count: length(bridge.span_lengths || [])
      }
    end)

    {:noreply, push_event(socket, "load_bridges", %{bridges: bridge_data})}
  end

  @impl true
  def handle_event("request_routes", _params, socket) do
    routes = Routes.list_routes()

    # Convert routes to map-friendly format
    route_data = Enum.map(routes, fn route ->
      %{
        id: route.id,
        name: route.name,
        coordinates: route.coordinates,
        analysis_status: route.analysis_status,
        total_length: route.total_length && Decimal.to_float(route.total_length)
      }
    end)

    {:noreply, push_event(socket, "load_routes", %{routes: route_data})}
  end

  @impl true
  def handle_event("bridge_clicked", %{"bridge_id" => bridge_id}, socket) do
    bridge = Bridges.get_bridge!(bridge_id)

    socket =
      socket
      |> assign(:selected_bridge, bridge)
      |> assign(:show_analysis_results, false)

    # Highlight the clicked bridge
    bridge_data = %{
      id: bridge.id,
      name: bridge.name,
      latitude: bridge.latitude && Decimal.to_float(bridge.latitude),
      longitude: bridge.longitude && Decimal.to_float(bridge.longitude),
      condition_rating: bridge.condition_rating,
      load_rating: bridge.load_rating && Decimal.to_float(bridge.load_rating),
      span_count: length(bridge.span_lengths || [])
    }

    socket = push_event(socket, "highlight_bridge", bridge_data)

    {:noreply, socket}
  end

  @impl true
  def handle_event("route_clicked", %{"route_id" => route_id}, socket) do
    route = Routes.get_route!(route_id)

    socket =
      socket
      |> assign(:selected_route, route)
      |> assign(:show_analysis_results, false)

    {:noreply, socket}
  end

  @impl true
  def handle_event("route_drawn", %{"coordinates" => coordinates, "type" => "LineString"}, socket) do
    # Create a new route from drawn coordinates
    route_name = "Route #{System.system_time(:second)}"

    case Routes.create_route_from_coordinates(route_name, coordinates, socket.assigns.current_user) do
      {:ok, route} ->
        # Find intersecting bridges
        intersecting_bridges = Routes.find_intersecting_bridges(route)

        socket =
          socket
          |> assign(:selected_route, route)
          |> put_flash(:info, "Route created! Found #{length(intersecting_bridges)} intersecting bridges.")
          |> load_routes()

        {:noreply, socket}

      {:error, changeset} ->
        {:noreply, put_flash(socket, :error, "Failed to create route: #{inspect(changeset.errors)}")}
    end
  end

  @impl true
  def handle_event("route_intersections", %{"intersecting_bridges" => bridge_ids}, socket) do
    # Handle real-time intersection detection from JavaScript
    intersecting_bridges = Enum.map(bridge_ids, &Bridges.get_bridge!/1)

    socket =
      socket
      |> put_flash(:info, "Found #{length(intersecting_bridges)} bridges along route")

    {:noreply, socket}
  end

  @impl true
  def handle_event("real_time_intersection", %{"coordinates" => coordinates}, socket) do
    # Throttle intersection detection to prevent excessive computation
    current_time = System.monotonic_time(:millisecond)
    last_intersection_time = socket.assigns[:last_intersection_time] || 0

    if current_time - last_intersection_time > 500 do  # Throttle to max 2 times per second
      # Perform server-side intersection detection for more accurate results
      bridge_ids = Starlight.Spatial.real_time_intersection_detection(coordinates)

      # Send results back to JavaScript
      socket =
        socket
        |> assign(:last_intersection_time, current_time)
        |> push_event("intersection_results", %{bridge_ids: bridge_ids})

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("show_bridge_form", _params, socket) do
    {:noreply, assign(socket, :show_bridge_form, true)}
  end

  @impl true
  def handle_event("hide_bridge_form", _params, socket) do
    {:noreply, assign(socket, :show_bridge_form, false)}
  end

  @impl true
  def handle_event("show_vehicle_form", _params, socket) do
    {:noreply, assign(socket, :show_vehicle_form, true)}
  end

  @impl true
  def handle_event("hide_vehicle_form", _params, socket) do
    {:noreply, assign(socket, :show_vehicle_form, false)}
  end

  @impl true
  def handle_event("analyze_route", %{"route_id" => route_id}, socket) do
    if socket.assigns.selected_vehicle do
      route = Routes.get_route!(route_id)

      # Validate route readiness for analysis
      validation = Starlight.Spatial.validate_route_analysis_readiness(route)

      if validation.ready_for_analysis do
        case Analysis.analyze_route(route_id, socket.assigns.selected_vehicle.id, socket.assigns.current_user) do
          {:ok, results} ->
            socket =
              socket
              |> assign(:analysis_results, results.successful_analyses)
              |> assign(:show_analysis_results, true)
              |> put_flash(:info, "Analysis completed! #{results.analyzed_bridges} bridges analyzed.")

            {:noreply, socket}

          {:error, reason} ->
            {:noreply, put_flash(socket, :error, "Analysis failed: #{reason}")}
        end
      else
        # Show validation errors
        missing_data_msg = format_missing_data_message(validation.missing_data)
        error_msg = "Route not ready for analysis. #{missing_data_msg}"

        {:noreply, put_flash(socket, :error, error_msg)}
      end
    else
      {:noreply, put_flash(socket, :error, "Please select a vehicle first")}
    end
  end

  @impl true
  def handle_event("select_vehicle", %{"vehicle_id" => vehicle_id}, socket) do
    vehicle = Vehicles.get_vehicle!(vehicle_id)

    socket =
      socket
      |> assign(:selected_vehicle, vehicle)
      |> assign(:show_vehicle_form, false)
      |> put_flash(:info, "Vehicle selected: #{vehicle.name}")

    {:noreply, socket}
  end

  @impl true
  def handle_event("clear_selection", _params, socket) do
    socket =
      socket
      |> assign(:selected_bridge, nil)
      |> assign(:selected_route, nil)
      |> assign(:show_analysis_results, false)
      |> push_event("clear_highlights", %{})

    {:noreply, socket}
  end

  # Handle component events
  @impl true
  def handle_info({:bridge_created, bridge}, socket) do
    socket =
      socket
      |> assign(:show_bridge_form, false)
      |> put_flash(:info, "Bridge created successfully!")
      |> load_bridges()

    {:noreply, socket}
  end

  @impl true
  def handle_info({:vehicle_created, vehicle}, socket) do
    socket =
      socket
      |> assign(:show_vehicle_form, false)
      |> assign(:selected_vehicle, vehicle)
      |> put_flash(:info, "Vehicle created successfully!")

    {:noreply, socket}
  end

  @impl true
  def handle_info({:vehicle_selected, vehicle}, socket) do
    socket =
      socket
      |> assign(:show_vehicle_form, false)
      |> assign(:selected_vehicle, vehicle)
      |> put_flash(:info, "Vehicle selected: #{vehicle.name}")

    {:noreply, socket}
  end

  @impl true
  def handle_info(:hide_bridge_form, socket) do
    {:noreply, assign(socket, :show_bridge_form, false)}
  end

  @impl true
  def handle_info(:hide_vehicle_form, socket) do
    {:noreply, assign(socket, :show_vehicle_form, false)}
  end

  # PubSub event handlers
  @impl true
  def handle_info({:bridge_updated, _bridge}, socket) do
    {:noreply, load_bridges(socket)}
  end

  @impl true
  def handle_info({:route_updated, _route}, socket) do
    {:noreply, load_routes(socket)}
  end

  @impl true
  def handle_info({:analysis_completed, _result}, socket) do
    {:noreply, load_routes(socket)}
  end

  @impl true
  def handle_info({:highlight_bridge, bridge}, socket) do
    # Convert bridge data to map-friendly format for highlighting
    bridge_data = %{
      id: bridge.id,
      name: bridge.name,
      latitude: bridge.latitude && Decimal.to_float(bridge.latitude),
      longitude: bridge.longitude && Decimal.to_float(bridge.longitude),
      condition_rating: bridge.condition_rating,
      load_rating: bridge.load_rating && Decimal.to_float(bridge.load_rating),
      span_count: length(bridge.span_lengths || [])
    }

    {:noreply, push_event(socket, "highlight_bridge", bridge_data)}
  end

  # Private helper functions
  defp load_initial_data(socket) do
    socket
    |> load_bridges()
    |> load_routes()
  end

  defp load_bridges(socket) do
    bridges = Bridges.list_bridges()
    assign(socket, :bridges, bridges)
  end

  defp load_routes(socket) do
    routes = Routes.list_routes()
    assign(socket, :routes, routes)
  end

  defp format_missing_data_message(missing_data) when length(missing_data) == 0, do: ""
  defp format_missing_data_message(missing_data) do
    bridge_messages = Enum.map(missing_data, fn %{bridge_name: name, missing_fields: fields} ->
      "#{name}: missing #{Enum.join(fields, ", ")}"
    end)

    "Missing data for bridges: #{Enum.join(bridge_messages, "; ")}"
  end
end
