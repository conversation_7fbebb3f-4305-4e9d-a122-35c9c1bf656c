<div class="h-screen flex flex-col">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Bridge Analysis Map</h1>
        <p class="text-sm text-gray-600">Draw routes and analyze bridge loading</p>
      </div>
      
      <div class="flex items-center space-x-4">
        <!-- Vehicle Selection -->
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700">Vehicle:</label>
          <%= if @selected_vehicle do %>
            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
              <%= @selected_vehicle.name %>
            </span>
            <button 
              phx-click="show_vehicle_form"
              class="text-blue-600 hover:text-blue-800 text-sm"
            >
              Change
            </button>
          <% else %>
            <button 
              phx-click="show_vehicle_form"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
            >
              Select Vehicle
            </button>
          <% end %>
        </div>
        
        <!-- Action Buttons -->
        <button 
          phx-click="show_bridge_form"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
        >
          Add Bridge
        </button>
        
        <%= if @selected_bridge || @selected_route do %>
          <button 
            phx-click="clear_selection"
            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm"
          >
            Clear Selection
          </button>
        <% end %>
      </div>
    </div>
  </div>
  
  <!-- Main Content -->
  <div class="flex-1 flex">
    <!-- Map Container -->
    <div class="flex-1 p-6">
      <div 
        id="map" 
        phx-hook="MapHook" 
        class="map-container"
        data-lat="-25.2744"
        data-lng="133.7751"
        data-zoom="6"
      >
      </div>
    </div>
    
    <!-- Sidebar -->
    <div class="w-96 bg-gray-50 border-l border-gray-200 overflow-y-auto">
      <!-- Selected Bridge Info -->
      <%= if @selected_bridge do %>
        <div class="p-6 bg-white border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Bridge Details</h3>
          
          <div class="space-y-3">
            <div>
              <label class="text-sm font-medium text-gray-700">Name</label>
              <p class="text-sm text-gray-900"><%= @selected_bridge.name %></p>
            </div>
            
            <%= if @selected_bridge.description do %>
              <div>
                <label class="text-sm font-medium text-gray-700">Description</label>
                <p class="text-sm text-gray-900"><%= @selected_bridge.description %></p>
              </div>
            <% end %>
            
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="text-sm font-medium text-gray-700">Condition</label>
                <p class="text-sm text-gray-900 capitalize">
                  <%= @selected_bridge.condition_rating || "Unknown" %>
                </p>
              </div>
              
              <div>
                <label class="text-sm font-medium text-gray-700">Load Rating</label>
                <p class="text-sm text-gray-900">
                  <%= if @selected_bridge.load_rating do %>
                    <%= Decimal.to_string(@selected_bridge.load_rating) %> kN
                  <% else %>
                    N/A
                  <% end %>
                </p>
              </div>
            </div>
            
            <div>
              <label class="text-sm font-medium text-gray-700">Spans</label>
              <p class="text-sm text-gray-900">
                <%= length(@selected_bridge.span_lengths || []) %> spans
              </p>
            </div>
            
            <%= if @selected_bridge.span_lengths do %>
              <div>
                <label class="text-sm font-medium text-gray-700">Span Lengths (m)</label>
                <p class="text-sm text-gray-900">
                  <%= Enum.map(@selected_bridge.span_lengths, &Decimal.to_string/1) |> Enum.join(", ") %>
                </p>
              </div>
            <% end %>
            
            <!-- Analysis Status -->
            <div class="pt-4 border-t border-gray-200">
              <%= if Starlight.Bridges.Bridge.complete_for_analysis?(@selected_bridge) do %>
                <div class="flex items-center text-green-600">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  Ready for analysis
                </div>
              <% else %>
                <div class="flex items-center text-yellow-600">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                  </svg>
                  Incomplete data
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
      
      <!-- Selected Route Info -->
      <%= if @selected_route do %>
        <div class="p-6 bg-white border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Route Details</h3>
          
          <div class="space-y-3">
            <div>
              <label class="text-sm font-medium text-gray-700">Name</label>
              <p class="text-sm text-gray-900"><%= @selected_route.name %></p>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="text-sm font-medium text-gray-700">Status</label>
                <span class={"text-sm px-2 py-1 rounded-full route-status-#{@selected_route.analysis_status}"}>
                  <%= String.capitalize(@selected_route.analysis_status) %>
                </span>
              </div>
              
              <div>
                <label class="text-sm font-medium text-gray-700">Length</label>
                <p class="text-sm text-gray-900">
                  <%= if @selected_route.total_length do %>
                    <%= Float.round(Decimal.to_float(@selected_route.total_length) / 1000, 2) %> km
                  <% else %>
                    N/A
                  <% end %>
                </p>
              </div>
            </div>
            
            <!-- Analyze Button -->
            <%= if @selected_vehicle && @selected_route.analysis_status in ["pending", "error"] do %>
              <button 
                phx-click="analyze_route"
                phx-value-route_id={@selected_route.id}
                class="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Analyze Route
              </button>
            <% end %>
          </div>
        </div>
      <% end %>
      
      <!-- Analysis Results -->
      <%= if @show_analysis_results && length(@analysis_results) > 0 do %>
        <div class="p-6 bg-white border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Analysis Results</h3>
          
          <div class="space-y-4">
            <%= for result <- @analysis_results do %>
              <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                  <h4 class="font-medium text-gray-900"><%= result.bridge.name %></h4>
                  <span class={
                    "px-2 py-1 rounded-full text-xs " <>
                    if result.pass_fail_status do
                      "bg-green-100 text-green-800"
                    else
                      "bg-red-100 text-red-800"
                    end
                  }>
                    <%= if result.pass_fail_status, do: "PASS", else: "FAIL" %>
                  </span>
                </div>
                
                <div class="text-sm text-gray-600 space-y-1">
                  <%= if result.max_moment do %>
                    <p>Max Moment: <%= Decimal.to_string(result.max_moment) %> N⋅m</p>
                  <% end %>
                  
                  <%= if result.max_deflection do %>
                    <p>Max Deflection: <%= Float.round(result.max_deflection * 1000, 2) %> mm</p>
                  <% end %>
                  
                  <%= if result.overall_safety_factor do %>
                    <p>Safety Factor: <%= Decimal.to_string(result.overall_safety_factor) %></p>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
      
      <!-- Instructions -->
      <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Instructions</h3>
        <div class="text-sm text-gray-600 space-y-2">
          <p>1. Select a vehicle from the dropdown above</p>
          <p>2. Use the drawing tool to draw a route on the map</p>
          <p>3. Click "Analyze Route" to run bridge analysis</p>
          <p>4. View results in the sidebar</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modals -->
<%= if @show_bridge_form do %>
  <.live_component 
    module={BridgeFormComponent} 
    id="bridge-form"
    current_user={@current_user}
  />
<% end %>

<%= if @show_vehicle_form do %>
  <.live_component 
    module={VehicleFormComponent} 
    id="vehicle-form"
    current_user={@current_user}
  />
<% end %>
