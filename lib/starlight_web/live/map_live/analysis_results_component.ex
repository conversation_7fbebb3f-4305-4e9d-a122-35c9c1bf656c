defmodule StarlightWeb.MapLive.AnalysisResultsComponent do
  @moduledoc """
  LiveView component for displaying analysis results.
  """
  
  use StarlightWeb, :live_component
  
  alias <PERSON>light.Analysis.AnalysisResult
  
  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
    
    {:ok, socket}
  end
  
  @impl true
  def handle_event("close", _params, socket) do
    send(self(), :hide_analysis_results)
    {:noreply, socket}
  end
  
  @impl true
  def render(assigns) do
    ~H"""
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white max-h-screen overflow-y-auto">
        <div class="mt-3">
          <!-- Header -->
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Analysis Results</h3>
            <button 
              phx-click="close" 
              phx-target={@myself}
              class="text-gray-400 hover:text-gray-600"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <!-- Results Summary -->
          <div class="mb-6 p-4 bg-gray-50 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-2">Summary</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span class="text-gray-600">Total Bridges:</span>
                <span class="font-medium ml-2"><%= length(@results) %></span>
              </div>
              <div>
                <span class="text-gray-600">Passing:</span>
                <span class="font-medium ml-2 text-green-600">
                  <%= Enum.count(@results, & &1.pass_fail_status) %>
                </span>
              </div>
              <div>
                <span class="text-gray-600">Failing:</span>
                <span class="font-medium ml-2 text-red-600">
                  <%= Enum.count(@results, &(not &1.pass_fail_status)) %>
                </span>
              </div>
            </div>
          </div>
          
          <!-- Individual Results -->
          <div class="space-y-6">
            <%= for result <- @results do %>
              <div class="border border-gray-200 rounded-lg p-6">
                <!-- Bridge Header -->
                <div class="flex items-center justify-between mb-4">
                  <div>
                    <h5 class="text-lg font-medium text-gray-900"><%= result.bridge.name %></h5>
                    <p class="text-sm text-gray-600">
                      Vehicle: <%= result.vehicle.name %> | 
                      Analyzed: <%= Calendar.strftime(result.calculated_at, "%Y-%m-%d %H:%M") %>
                    </p>
                  </div>
                  
                  <span class={
                    "px-3 py-1 rounded-full text-sm font-medium " <>
                    if result.pass_fail_status do
                      "bg-green-100 text-green-800"
                    else
                      "bg-red-100 text-red-800"
                    end
                  }>
                    <%= if result.pass_fail_status, do: "PASS", else: "FAIL" %>
                  </span>
                </div>
                
                <!-- Key Results -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                  <%= if result.max_moment do %>
                    <div class="bg-blue-50 p-3 rounded">
                      <p class="text-xs text-blue-600 font-medium">MAX MOMENT</p>
                      <p class="text-lg font-semibold text-blue-900">
                        <%= format_number(Decimal.to_float(result.max_moment)) %> N⋅m
                      </p>
                    </div>
                  <% end %>
                  
                  <%= if result.max_stress do %>
                    <div class="bg-purple-50 p-3 rounded">
                      <p class="text-xs text-purple-600 font-medium">MAX STRESS</p>
                      <p class="text-lg font-semibold text-purple-900">
                        <%= format_number(Decimal.to_float(result.max_stress)) %> Pa
                      </p>
                    </div>
                  <% end %>
                  
                  <%= if result.max_deflection do %>
                    <div class="bg-orange-50 p-3 rounded">
                      <p class="text-xs text-orange-600 font-medium">MAX DEFLECTION</p>
                      <p class="text-lg font-semibold text-orange-900">
                        <%= Float.round(result.max_deflection * 1000, 2) %> mm
                      </p>
                    </div>
                  <% end %>
                  
                  <%= if result.overall_safety_factor do %>
                    <div class={
                      "p-3 rounded " <>
                      if Decimal.to_float(result.overall_safety_factor) >= 1.0 do
                        "bg-green-50"
                      else
                        "bg-red-50"
                      end
                    }>
                      <p class={
                        "text-xs font-medium " <>
                        if Decimal.to_float(result.overall_safety_factor) >= 1.0 do
                          "text-green-600"
                        else
                          "text-red-600"
                        end
                      }>SAFETY FACTOR</p>
                      <p class={
                        "text-lg font-semibold " <>
                        if Decimal.to_float(result.overall_safety_factor) >= 1.0 do
                          "text-green-900"
                        else
                          "text-red-900"
                        end
                      }>
                        <%= Decimal.to_string(result.overall_safety_factor) %>
                      </p>
                    </div>
                  <% end %>
                </div>
                
                <!-- Detailed Results -->
                <div class="border-t pt-4">
                  <h6 class="text-sm font-medium text-gray-900 mb-2">Detailed Analysis</h6>
                  
                  <!-- Moments at Supports -->
                  <%= if result.moment_values["moments"] do %>
                    <div class="mb-3">
                      <p class="text-xs text-gray-600 font-medium mb-1">MOMENTS AT SUPPORTS (N⋅m)</p>
                      <div class="flex flex-wrap gap-2">
                        <%= for {moment, index} <- Enum.with_index(result.moment_values["moments"]) do %>
                          <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">
                            Support <%= index %>: <%= format_number(moment) %>
                          </span>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                  
                  <!-- Reaction Forces -->
                  <%= if result.reaction_forces["reactions"] do %>
                    <div class="mb-3">
                      <p class="text-xs text-gray-600 font-medium mb-1">REACTION FORCES (N)</p>
                      <div class="flex flex-wrap gap-2">
                        <%= for {reaction, index} <- Enum.with_index(result.reaction_forces["reactions"]) do %>
                          <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">
                            Support <%= index %>: <%= format_number(reaction) %>
                          </span>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                  
                  <!-- Notes -->
                  <%= if result.calculation_notes do %>
                    <div class="mt-3 p-3 bg-yellow-50 rounded">
                      <p class="text-xs text-yellow-600 font-medium">NOTES</p>
                      <p class="text-sm text-yellow-800"><%= result.calculation_notes %></p>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
          
          <!-- Footer -->
          <div class="flex justify-end mt-6 pt-6 border-t">
            <button 
              phx-click="close" 
              phx-target={@myself}
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
    """
  end
  
  # Helper function to format numbers
  defp format_number(number) when is_float(number) do
    :erlang.float_to_binary(number, decimals: 2)
  end
  
  defp format_number(number) when is_integer(number) do
    Integer.to_string(number)
  end
  
  defp format_number(number) do
    to_string(number)
  end
end
