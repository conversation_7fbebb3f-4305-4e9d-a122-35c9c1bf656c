defmodule StarlightWeb.MapLive.VehicleFormComponent do
  @moduledoc """
  LiveView component for selecting or creating vehicle data.
  """

  use StarlightWeb, :live_component

  alias Starlight.Vehicles
  alias Starlight.Vehicles.Vehicle

  @impl true
  def update(assigns, socket) do
    vehicles = Vehicles.list_vehicles()
    standard_vehicles = Vehicles.list_standard_vehicles()
    changeset = Vehicles.change_vehicle(%Vehicle{})

    socket =
      socket
      |> assign(assigns)
      |> assign(:vehicles, vehicles)
      |> assign(:standard_vehicles, standard_vehicles)
      |> assign(:changeset, changeset)
      |> assign(:vehicle, %Vehicle{})
      |> assign(:mode, :select)  # :select or :create

    {:ok, socket}
  end

  @impl true
  def handle_event("select_vehicle", %{"vehicle_id" => vehicle_id}, socket) do
    vehicle = Vehicles.get_vehicle!(vehicle_id)
    send(self(), {:vehicle_selected, vehicle})
    {:noreply, socket}
  end

  @impl true
  def handle_event("switch_to_create", _params, socket) do
    {:noreply, assign(socket, :mode, :create)}
  end

  @impl true
  def handle_event("switch_to_select", _params, socket) do
    {:noreply, assign(socket, :mode, :select)}
  end

  @impl true
  def handle_event("validate", %{"vehicle" => vehicle_params}, socket) do
    changeset =
      socket.assigns.vehicle
      |> Vehicles.change_vehicle(vehicle_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  @impl true
  def handle_event("save", %{"vehicle" => vehicle_params}, socket) do
    # Process comma-separated inputs
    vehicle_params = process_vehicle_params(vehicle_params)

    # Add current user to params
    vehicle_params = Map.put(vehicle_params, "created_by", socket.assigns.current_user)

    case Vehicles.create_vehicle(vehicle_params) do
      {:ok, vehicle} ->
        # Notify parent LiveView
        send(self(), {:vehicle_created, vehicle})
        {:noreply, socket}

      {:error, changeset} ->
        {:noreply, assign(socket, :changeset, changeset)}
    end
  end

  @impl true
  def handle_event("close", _params, socket) do
    send(self(), :hide_vehicle_form)
    {:noreply, socket}
  end

  # Private helper functions
  defp process_vehicle_params(params) do
    params
    |> process_axle_loads()
    |> process_wheelbase_dimensions()
  end

  defp process_axle_loads(params) do
    case Map.get(params, "axle_loads_input") do
      nil -> params
      "" -> params
      input ->
        axle_loads =
          input
          |> String.split(",")
          |> Enum.map(&String.trim/1)
          |> Enum.map(&Decimal.new/1)

        params
        |> Map.put("axle_loads", axle_loads)
        |> Map.delete("axle_loads_input")
    end
  end

  defp process_wheelbase_dimensions(params) do
    case Map.get(params, "wheelbase_dimensions_input") do
      nil -> params
      "" -> params
      input ->
        wheelbase_dimensions =
          input
          |> String.split(",")
          |> Enum.map(&String.trim/1)
          |> Enum.map(&Decimal.new/1)

        params
        |> Map.put("wheelbase_dimensions", wheelbase_dimensions)
        |> Map.delete("wheelbase_dimensions_input")
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white max-h-screen overflow-y-auto">
        <div class="mt-3">
          <!-- Header -->
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">
              <%= if @mode == :select, do: "Select Vehicle", else: "Create New Vehicle" %>
            </h3>
            <button
              phx-click="close"
              phx-target={@myself}
              class="text-gray-400 hover:text-gray-600"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Mode Toggle -->
          <div class="flex space-x-4 mb-6">
            <button
              phx-click="switch_to_select"
              phx-target={@myself}
              class={
                "px-4 py-2 text-sm font-medium rounded-md " <>
                if @mode == :select do
                  "bg-blue-600 text-white"
                else
                  "bg-gray-100 text-gray-700 hover:bg-gray-200"
                end
              }
            >
              Select Existing
            </button>

            <button
              phx-click="switch_to_create"
              phx-target={@myself}
              class={
                "px-4 py-2 text-sm font-medium rounded-md " <>
                if @mode == :create do
                  "bg-blue-600 text-white"
                else
                  "bg-gray-100 text-gray-700 hover:bg-gray-200"
                end
              }
            >
              Create New
            </button>
          </div>

          <%= if @mode == :select do %>
            <!-- Vehicle Selection -->
            <div class="space-y-6">
              <!-- Standard Vehicles -->
              <%= if length(@standard_vehicles) > 0 do %>
                <div>
                  <h4 class="text-md font-medium text-gray-900 mb-4">Standard Design Vehicles</h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <%= for vehicle <- @standard_vehicles do %>
                      <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer"
                           phx-click="select_vehicle"
                           phx-value-vehicle_id={vehicle.id}
                           phx-target={@myself}>
                        <h5 class="font-medium text-gray-900"><%= vehicle.name %></h5>
                        <p class="text-sm text-gray-600"><%= vehicle.vehicle_classification %></p>
                        <p class="text-sm text-gray-600">
                          <%= Decimal.to_string(vehicle.gross_vehicle_weight) %> kN,
                          <%= length(vehicle.axle_loads) %> axles
                        </p>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>

              <!-- Custom Vehicles -->
              <%= if length(@vehicles) > length(@standard_vehicles) do %>
                <div>
                  <h4 class="text-md font-medium text-gray-900 mb-4">Custom Vehicles</h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <%= for vehicle <- Enum.reject(@vehicles, & &1.is_standard_vehicle) do %>
                      <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer"
                           phx-click="select_vehicle"
                           phx-value-vehicle_id={vehicle.id}
                           phx-target={@myself}>
                        <h5 class="font-medium text-gray-900"><%= vehicle.name %></h5>
                        <p class="text-sm text-gray-600"><%= vehicle.vehicle_classification %></p>
                        <p class="text-sm text-gray-600">
                          <%= Decimal.to_string(vehicle.gross_vehicle_weight) %> kN,
                          <%= length(vehicle.axle_loads) %> axles
                        </p>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <!-- Vehicle Creation Form -->
            <.form
              for={@changeset}
              phx-change="validate"
              phx-submit="save"
              phx-target={@myself}
              class="space-y-6"
            >
              <!-- Basic Information -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <.input
                    field={@changeset[:name]}
                    type="text"
                    label="Vehicle Name"
                    required
                  />
                </div>

                <div>
                  <.input
                    field={@changeset[:vehicle_classification]}
                    type="select"
                    label="Classification"
                    required
                    options={[
                      {"Single Unit", "Single_Unit"},
                      {"B-Double", "B_Double"},
                      {"Road Train", "Road_Train"},
                      {"Semi-Trailer", "Semi_Trailer"},
                      {"Truck Trailer", "Truck_Trailer"},
                      {"Rigid Truck", "Rigid_Truck"},
                      {"Articulated Truck", "Articulated_Truck"},
                      {"Special Vehicle", "Special_Vehicle"}
                    ]}
                  />
                </div>
              </div>

              <div>
                <.input
                  field={@changeset[:description]}
                  type="textarea"
                  label="Description"
                  rows="2"
                />
              </div>

              <!-- Vehicle Specifications -->
              <div class="border-t pt-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">Vehicle Specifications</h4>

                <div class="space-y-4">
                  <div>
                    <.input
                      field={@changeset[:gross_vehicle_weight]}
                      type="number"
                      label="Gross Vehicle Weight (kN)"
                      step="any"
                      required
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Axle Loads (kN, comma-separated)
                    </label>
                    <input
                      type="text"
                      name="vehicle[axle_loads_input]"
                      placeholder="e.g., 6.0, 17.0, 17.0, 17.0"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <p class="text-xs text-gray-500 mt-1">Enter axle loads separated by commas</p>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Wheelbase Dimensions (m, comma-separated)
                    </label>
                    <input
                      type="text"
                      name="vehicle[wheelbase_dimensions_input]"
                      placeholder="e.g., 3.7, 1.3, 6.0"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <p class="text-xs text-gray-500 mt-1">Distances between axles (one less than number of axles)</p>
                  </div>
                </div>
              </div>

              <!-- Physical Dimensions -->
              <div class="border-t pt-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">Physical Dimensions (Optional)</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <.input
                      field={@changeset[:vehicle_length]}
                      type="number"
                      label="Length (m)"
                      step="any"
                    />
                  </div>

                  <div>
                    <.input
                      field={@changeset[:vehicle_width]}
                      type="number"
                      label="Width (m)"
                      step="any"
                    />
                  </div>

                  <div>
                    <.input
                      field={@changeset[:vehicle_height]}
                      type="number"
                      label="Height (m)"
                      step="any"
                    />
                  </div>
                </div>
              </div>

              <!-- Form Actions -->
              <div class="flex items-center justify-end space-x-4 pt-6 border-t">
                <button
                  type="button"
                  phx-click="close"
                  phx-target={@myself}
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
                >
                  Cancel
                </button>

                <button
                  type="submit"
                  class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
                >
                  Create Vehicle
                </button>
              </div>
            </.form>
          <% end %>
        </div>
      </div>
    </div>
    """
  end
end
