defmodule StarlightWeb.MapLive.BridgeFormComponent do
  @moduledoc """
  LiveView component for creating and editing bridge data.
  """

  use StarlightWeb, :live_component

  alias Starlight.Bridges
  alias Starlight.Bridges.Bridge

  @impl true
  def update(assigns, socket) do
    changeset = Bridges.change_bridge(%Bridge{})

    socket =
      socket
      |> assign(assigns)
      |> assign(:changeset, changeset)
      |> assign(:bridge, %Bridge{})

    {:ok, socket}
  end

  @impl true
  def handle_event("validate", %{"bridge" => bridge_params}, socket) do
    changeset =
      socket.assigns.bridge
      |> Bridges.change_bridge(bridge_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  @impl true
  def handle_event("save", %{"bridge" => bridge_params}, socket) do
    # Process comma-separated inputs
    bridge_params = process_bridge_params(bridge_params)

    # Add current user to params
    bridge_params = Map.put(bridge_params, "created_by", socket.assigns.current_user)

    case Bridges.create_bridge(bridge_params) do
      {:ok, bridge} ->
        # Notify parent LiveView
        send(self(), {:bridge_created, bridge})
        {:noreply, socket}

      {:error, changeset} ->
        {:noreply, assign(socket, :changeset, changeset)}
    end
  end

  @impl true
  def handle_event("close", _params, socket) do
    send(self(), :hide_bridge_form)
    {:noreply, socket}
  end

  # Private helper functions
  defp process_bridge_params(params) do
    params
    |> process_span_lengths()
    |> process_support_conditions()
    |> process_moment_of_inertia()
  end

  defp process_span_lengths(params) do
    case Map.get(params, "span_lengths_input") do
      nil -> params
      "" -> params
      input ->
        span_lengths =
          input
          |> String.split(",")
          |> Enum.map(&String.trim/1)
          |> Enum.map(&Decimal.new/1)

        params
        |> Map.put("span_lengths", span_lengths)
        |> Map.delete("span_lengths_input")
    end
  end

  defp process_support_conditions(params) do
    case Map.get(params, "support_conditions_input") do
      nil -> params
      "" -> params
      input ->
        support_conditions =
          input
          |> String.split(",")
          |> Enum.map(&String.trim/1)

        params
        |> Map.put("support_conditions", support_conditions)
        |> Map.delete("support_conditions_input")
    end
  end

  defp process_moment_of_inertia(params) do
    case Map.get(params, "moment_of_inertia_input") do
      nil -> params
      "" -> params
      input ->
        moment_of_inertia =
          input
          |> String.split(",")
          |> Enum.map(&String.trim/1)
          |> Enum.map(&Decimal.new/1)

        params
        |> Map.put("moment_of_inertia", moment_of_inertia)
        |> Map.delete("moment_of_inertia_input")
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <!-- Header -->
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Add New Bridge</h3>
            <button
              phx-click="close"
              phx-target={@myself}
              class="text-gray-400 hover:text-gray-600"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Form -->
          <.form
            for={@changeset}
            phx-change="validate"
            phx-submit="save"
            phx-target={@myself}
            class="space-y-6"
          >
            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <.input
                  field={@changeset[:name]}
                  type="text"
                  label="Bridge Name"
                  required
                />
              </div>

              <div>
                <.input
                  field={@changeset[:condition_rating]}
                  type="select"
                  label="Condition Rating"
                  options={[
                    {"Excellent", "excellent"},
                    {"Good", "good"},
                    {"Fair", "fair"},
                    {"Poor", "poor"}
                  ]}
                />
              </div>
            </div>

            <div>
              <.input
                field={@changeset[:description]}
                type="textarea"
                label="Description"
                rows="3"
              />
            </div>

            <!-- Location -->
            <div class="border-t pt-6">
              <h4 class="text-md font-medium text-gray-900 mb-4">Location</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <.input
                    field={@changeset[:latitude]}
                    type="number"
                    label="Latitude"
                    step="any"
                    required
                  />
                </div>

                <div>
                  <.input
                    field={@changeset[:longitude]}
                    type="number"
                    label="Longitude"
                    step="any"
                    required
                  />
                </div>
              </div>
            </div>

            <!-- Structural Properties -->
            <div class="border-t pt-6">
              <h4 class="text-md font-medium text-gray-900 mb-4">Structural Properties</h4>

              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Span Lengths (meters, comma-separated)
                  </label>
                  <input
                    type="text"
                    name="bridge[span_lengths_input]"
                    placeholder="e.g., 25.0, 30.0, 25.0"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <p class="text-xs text-gray-500 mt-1">Enter span lengths separated by commas</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Support Conditions (comma-separated)
                  </label>
                  <input
                    type="text"
                    name="bridge[support_conditions_input]"
                    placeholder="e.g., fixed, pinned, pinned, fixed"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <p class="text-xs text-gray-500 mt-1">Options: fixed, pinned, roller</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <.input
                      field={@changeset[:elastic_modulus]}
                      type="number"
                      label="Elastic Modulus (GPa)"
                      step="any"
                      required
                    />
                  </div>

                  <div>
                    <.input
                      field={@changeset[:load_rating]}
                      type="number"
                      label="Load Rating (kN)"
                      step="any"
                    />
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Moment of Inertia (m⁴, comma-separated)
                  </label>
                  <input
                    type="text"
                    name="bridge[moment_of_inertia_input]"
                    placeholder="e.g., 0.001, 0.0012, 0.001"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <p class="text-xs text-gray-500 mt-1">One value per span</p>
                </div>
              </div>
            </div>

            <!-- Additional Information -->
            <div class="border-t pt-6">
              <h4 class="text-md font-medium text-gray-900 mb-4">Additional Information</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <.input
                    field={@changeset[:year_built]}
                    type="number"
                    label="Year Built"
                  />
                </div>

                <div>
                  <.input
                    field={@changeset[:last_inspection]}
                    type="date"
                    label="Last Inspection"
                  />
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t">
              <button
                type="button"
                phx-click="close"
                phx-target={@myself}
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>

              <button
                type="submit"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
              >
                Create Bridge
              </button>
            </div>
          </.form>
        </div>
      </div>
    </div>
    """
  end
end
