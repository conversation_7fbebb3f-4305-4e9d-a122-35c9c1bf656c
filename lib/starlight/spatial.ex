defmodule Starlight.Spatial do
  @moduledoc """
  Spatial query utilities for bridge and route intersection detection.

  This module provides spatial operations that work with lat/lng coordinates
  and can be upgraded to use PostGIS when available.
  """

  alias Starlight.{Bridges, Routes}
  alias Starlight.Bridges.Bridge
  alias Starlight.Routes.Route

  @earth_radius_km 6371.0
  @default_buffer_km 1.0  # Default buffer distance for intersection detection

  @doc """
  Finds bridges that intersect with a given route.

  Uses a combination of bounding box filtering and distance calculations
  to efficiently find intersections.
  """
  def find_bridges_intersecting_route(%Route{} = route, buffer_km \\ @default_buffer_km) do
    coordinates = Route.get_coordinates(route)

    if length(coordinates) < 2 do
      []
    else
      # Get all bridges with valid coordinates
      bridges = Bridges.list_bridges()
      |> Enum.filter(&has_valid_coordinates?/1)

      # Filter by bounding box first for efficiency
      bounding_box = calculate_bounding_box(coordinates, buffer_km)
      bridges_in_bbox = filter_bridges_by_bounding_box(bridges, bounding_box)

      # Then check precise distance to route
      Enum.filter(bridges_in_bbox, fn bridge ->
        bridge_intersects_route?(bridge, coordinates, buffer_km)
      end)
    end
  end

  @doc """
  Checks if a bridge intersects with a route within the given buffer distance.
  """
  def bridge_intersects_route?(%Bridge{} = bridge, route_coordinates, buffer_km \\ @default_buffer_km) do
    case Bridge.coordinates(bridge) do
      {lat, lng} ->
        bridge_point = {lat, lng}
        min_distance = minimum_distance_to_route(bridge_point, route_coordinates)
        min_distance <= buffer_km
      nil -> false
    end
  end

  @doc """
  Calculates the minimum distance from a point to a route (polyline).
  Returns distance in kilometers.
  """
  def minimum_distance_to_route({lat, lng}, route_coordinates) do
    route_coordinates
    |> Enum.chunk_every(2, 1, :discard)
    |> Enum.map(fn [[lng1, lat1], [lng2, lat2]] ->
      distance_point_to_line_segment({lat, lng}, {lat1, lng1}, {lat2, lng2})
    end)
    |> Enum.min()
  end

  @doc """
  Calculates the distance between two points using the Haversine formula.
  Returns distance in kilometers.
  """
  def haversine_distance({lat1, lng1}, {lat2, lng2}) do
    lat1_rad = degrees_to_radians(lat1)
    lat2_rad = degrees_to_radians(lat2)
    delta_lat = degrees_to_radians(lat2 - lat1)
    delta_lng = degrees_to_radians(lng2 - lng1)

    a = :math.sin(delta_lat / 2) * :math.sin(delta_lat / 2) +
        :math.cos(lat1_rad) * :math.cos(lat2_rad) *
        :math.sin(delta_lng / 2) * :math.sin(delta_lng / 2)

    c = 2 * :math.atan2(:math.sqrt(a), :math.sqrt(1 - a))

    @earth_radius_km * c
  end

  @doc """
  Calculates the distance from a point to a line segment.
  Returns distance in kilometers.
  """
  def distance_point_to_line_segment(point, line_start, line_end) do
    {px, py} = point
    {x1, y1} = line_start
    {x2, y2} = line_end

    # Vector from line start to point
    a = px - x1
    b = py - y1

    # Vector from line start to line end
    c = x2 - x1
    d = y2 - y1

    # Dot product and line length squared
    dot = a * c + b * d
    len_sq = c * c + d * d

    # Handle degenerate case (line segment is actually a point)
    if len_sq == 0 do
      haversine_distance(point, line_start)
    else
      # Parameter t represents position along line segment (0 = start, 1 = end)
      param = dot / len_sq

      closest_point = cond do
        param < 0 -> line_start  # Closest to start
        param > 1 -> line_end    # Closest to end
        true -> {x1 + param * c, y1 + param * d}  # On the line segment
      end

      haversine_distance(point, closest_point)
    end
  end

  @doc """
  Calculates a bounding box for a set of coordinates with a buffer.
  Returns {min_lat, max_lat, min_lng, max_lng}.
  """
  def calculate_bounding_box(coordinates, buffer_km \\ @default_buffer_km) do
    lats = Enum.map(coordinates, fn [_lng, lat] -> lat end)
    lngs = Enum.map(coordinates, fn [lng, _lat] -> lng end)

    min_lat = Enum.min(lats)
    max_lat = Enum.max(lats)
    min_lng = Enum.min(lngs)
    max_lng = Enum.max(lngs)

    # Convert buffer distance to approximate degrees
    # This is a rough approximation that works reasonably well for small areas
    lat_buffer = buffer_km / 111.0  # ~111 km per degree latitude
    lng_buffer = buffer_km / (111.0 * :math.cos(degrees_to_radians((min_lat + max_lat) / 2)))

    {
      min_lat - lat_buffer,
      max_lat + lat_buffer,
      min_lng - lng_buffer,
      max_lng + lng_buffer
    }
  end

  @doc """
  Filters bridges by bounding box for efficient spatial queries.
  """
  def filter_bridges_by_bounding_box(bridges, {min_lat, max_lat, min_lng, max_lng}) do
    Enum.filter(bridges, fn bridge ->
      case Bridge.coordinates(bridge) do
        {lat, lng} ->
          lat >= min_lat and lat <= max_lat and lng >= min_lng and lng <= max_lng
        nil -> false
      end
    end)
  end

  @doc """
  Validates that a bridge has valid coordinates for spatial operations.
  """
  def has_valid_coordinates?(%Bridge{} = bridge) do
    case Bridge.coordinates(bridge) do
      {lat, lng} when is_number(lat) and is_number(lng) ->
        lat >= -90 and lat <= 90 and lng >= -180 and lng <= 180
      _ -> false
    end
  end

  @doc """
  Validates that a route has valid geometry for spatial operations.
  """
  def has_valid_geometry?(%Route{} = route) do
    coordinates = Route.get_coordinates(route)
    length(coordinates) >= 2 and Enum.all?(coordinates, &valid_coordinate?/1)
  end

  @doc """
  Creates a spatial index for bridges (in-memory for now, can be upgraded to PostGIS).
  Returns a map with grid cells containing bridge lists for efficient querying.
  """
  def create_bridge_spatial_index(bridges, grid_size_degrees \\ 0.1) do
    bridges
    |> Enum.filter(&has_valid_coordinates?/1)
    |> Enum.group_by(fn bridge ->
      {lat, lng} = Bridge.coordinates(bridge)
      grid_cell = {
        Float.floor(lat / grid_size_degrees),
        Float.floor(lng / grid_size_degrees)
      }
      grid_cell
    end)
  end

  @doc """
  Queries the spatial index for bridges near a point.
  """
  def query_spatial_index(spatial_index, {lat, lng}, grid_size_degrees \\ 0.1, radius_cells \\ 1) do
    center_cell = {
      Float.floor(lat / grid_size_degrees),
      Float.floor(lng / grid_size_degrees)
    }

    {center_x, center_y} = center_cell

    # Get bridges from surrounding cells
    for x <- (center_x - radius_cells)..(center_x + radius_cells),
        y <- (center_y - radius_cells)..(center_y + radius_cells),
        bridges = Map.get(spatial_index, {x, y}, []),
        bridge <- bridges do
      bridge
    end
    |> Enum.uniq()
  end

  @doc """
  Performs real-time intersection detection as a route is being drawn.
  Returns a list of bridge IDs that intersect with the route.
  """
  def real_time_intersection_detection(route_coordinates, buffer_km \\ @default_buffer_km) do
    if length(route_coordinates) < 2 do
      []
    else
      # Create a temporary route-like structure
      temp_route = %{coordinates: %{"coordinates" => route_coordinates}}

      # Get all bridges and filter by intersection
      Bridges.list_bridges()
      |> Enum.filter(&has_valid_coordinates?/1)
      |> Enum.filter(fn bridge ->
        bridge_intersects_route?(bridge, route_coordinates, buffer_km)
      end)
      |> Enum.map(& &1.id)
    end
  end

  # Private helper functions

  defp degrees_to_radians(degrees), do: degrees * :math.pi() / 180

  defp valid_coordinate?([lng, lat]) when is_number(lng) and is_number(lat) do
    lat >= -90 and lat <= 90 and lng >= -180 and lng <= 180
  end
  defp valid_coordinate?(_), do: false

  @doc """
  Data validation pipeline for spatial operations.
  Validates bridges and routes for completeness and spatial accuracy.
  """
  def validate_spatial_data do
    %{
      bridges: validate_bridges_spatial_data(),
      routes: validate_routes_spatial_data()
    }
  end

  @doc """
  Validates bridge spatial data and returns validation results.
  """
  def validate_bridges_spatial_data do
    bridges = Bridges.list_bridges()

    %{
      total: length(bridges),
      valid_coordinates: Enum.count(bridges, &has_valid_coordinates?/1),
      complete_for_analysis: Enum.count(bridges, &Bridge.complete_for_analysis?/1),
      invalid_bridges: Enum.reject(bridges, &has_valid_coordinates?/1),
      incomplete_bridges: Enum.reject(bridges, &Bridge.complete_for_analysis?/1)
    }
  end

  @doc """
  Validates route spatial data and returns validation results.
  """
  def validate_routes_spatial_data do
    routes = Routes.list_routes()

    %{
      total: length(routes),
      valid_geometry: Enum.count(routes, &has_valid_geometry?/1),
      invalid_routes: Enum.reject(routes, &has_valid_geometry?/1)
    }
  end

  @doc """
  Validates that bridges along a route have complete data for analysis.
  Returns a detailed validation report.
  """
  def validate_route_analysis_readiness(%Route{} = route) do
    intersecting_bridges = find_bridges_intersecting_route(route)

    complete_bridges = Enum.filter(intersecting_bridges, &Bridge.complete_for_analysis?/1)
    incomplete_bridges = Enum.reject(intersecting_bridges, &Bridge.complete_for_analysis?/1)

    %{
      route_valid: has_valid_geometry?(route),
      total_bridges: length(intersecting_bridges),
      complete_bridges: length(complete_bridges),
      incomplete_bridges: length(incomplete_bridges),
      ready_for_analysis: has_valid_geometry?(route) and length(incomplete_bridges) == 0,
      missing_data: get_missing_bridge_data(incomplete_bridges)
    }
  end

  defp get_missing_bridge_data(bridges) do
    Enum.map(bridges, fn bridge ->
      missing_fields = []
      missing_fields = if is_nil(bridge.span_lengths) or length(bridge.span_lengths) == 0,
                         do: ["span_lengths" | missing_fields], else: missing_fields
      missing_fields = if is_nil(bridge.support_conditions),
                         do: ["support_conditions" | missing_fields], else: missing_fields
      missing_fields = if is_nil(bridge.elastic_modulus),
                         do: ["elastic_modulus" | missing_fields], else: missing_fields
      missing_fields = if is_nil(bridge.moment_of_inertia),
                         do: ["moment_of_inertia" | missing_fields], else: missing_fields

      %{
        bridge_id: bridge.id,
        bridge_name: bridge.name,
        missing_fields: missing_fields
      }
    end)
  end
end
