defmodule Starlight.Routes do
  @moduledoc """
  The Routes context for managing route data and operations.
  """

  import Ecto.Query, warn: false
  alias <PERSON>light.Repo
  alias Starlight.Routes.Route

  @doc """
  Returns the list of routes.

  ## Examples

      iex> list_routes()
      [%Route{}, ...]

  """
  def list_routes do
    Repo.all(Route)
  end

  @doc """
  Returns the list of routes ordered by most recent first.
  """
  def list_routes_recent_first do
    Route
    |> order_by([r], desc: r.inserted_at)
    |> Repo.all()
  end

  @doc """
  Returns routes filtered by analysis status.
  """
  def list_routes_by_status(status) do
    Route
    |> where([r], r.analysis_status == ^status)
    |> Repo.all()
  end

  @doc """
  Gets a single route.

  Raises `Ecto.NoResultsError` if the Route does not exist.

  ## Examples

      iex> get_route!(123)
      %Route{}

      iex> get_route!(456)
      ** (Ecto.NoResultsError)

  """
  def get_route!(id), do: Repo.get!(Route, id)

  @doc """
  Gets a single route by ID, returns nil if not found.
  """
  def get_route(id), do: Repo.get(Route, id)

  @doc """
  Creates a route.

  ## Examples

      iex> create_route(%{field: value})
      {:ok, %Route{}}

      iex> create_route(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_route(attrs \\ %{}) do
    %Route{}
    |> Route.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates a route from drawn coordinates.
  """
  def create_route_from_coordinates(name, coordinates, user_id \\ nil) do
    # Convert coordinates to GeoJSON format
    geojson_coords = %{
      "type" => "LineString",
      "coordinates" => coordinates
    }

    attrs = %{
      name: name,
      coordinates: geojson_coords,
      created_by: user_id,
      analysis_status: "pending"
    }

    create_route(attrs)
  end

  @doc """
  Updates a route.

  ## Examples

      iex> update_route(route, %{field: new_value})
      {:ok, %Route{}}

      iex> update_route(route, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_route(%Route{} = route, attrs) do
    route
    |> Route.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Updates the analysis status of a route.
  """
  def update_analysis_status(%Route{} = route, status) do
    route
    |> Route.update_analysis_status(status)
    |> Repo.update()
  end

  @doc """
  Deletes a route.

  ## Examples

      iex> delete_route(route)
      {:ok, %Route{}}

      iex> delete_route(route)
      {:error, %Ecto.Changeset{}}

  """
  def delete_route(%Route{} = route) do
    Repo.delete(route)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking route changes.

  ## Examples

      iex> change_route(route)
      %Ecto.Changeset{data: %Route{}}

  """
  def change_route(%Route{} = route, attrs \\ %{}) do
    Route.changeset(route, attrs)
  end

  @doc """
  Finds bridges that intersect with the given route.
  Uses the Bridges context to perform spatial queries.
  """
  def find_intersecting_bridges(%Route{} = route) do
    Starlight.Bridges.find_bridges_on_route(route)
  end

  @doc """
  Validates that a route has valid geometry for analysis.
  """
  def validate_for_analysis(%Route{} = route) do
    if Route.has_valid_geometry?(route) do
      {:ok, route}
    else
      {:error, "Route must have valid geometry with at least 2 coordinate points"}
    end
  end

  @doc """
  Returns routes that are ready for analysis (have valid geometry).
  """
  def list_routes_ready_for_analysis do
    list_routes()
    |> Enum.filter(&Route.has_valid_geometry?/1)
  end

  @doc """
  Returns routes that are currently being analyzed.
  """
  def list_routes_in_progress do
    list_routes_by_status("analyzing")
  end

  @doc """
  Returns routes that have completed analysis.
  """
  def list_completed_routes do
    list_routes_by_status("completed")
  end

  @doc """
  Returns routes that had analysis errors.
  """
  def list_error_routes do
    list_routes_by_status("error")
  end

  @doc """
  Calculates statistics for routes.
  """
  def route_statistics do
    routes = list_routes()
    
    %{
      total_routes: length(routes),
      pending: length(list_routes_by_status("pending")),
      analyzing: length(list_routes_by_status("analyzing")),
      completed: length(list_routes_by_status("completed")),
      error: length(list_routes_by_status("error")),
      total_length_km: calculate_total_length(routes)
    }
  end

  defp calculate_total_length(routes) do
    routes
    |> Enum.map(fn route ->
      case route.total_length do
        nil -> 0.0
        length -> Decimal.to_float(length)
      end
    end)
    |> Enum.sum()
    |> Kernel./(1000)  # Convert to kilometers
    |> Float.round(2)
  end

  @doc """
  Returns the most recently created routes (limit 10).
  """
  def recent_routes(limit \\ 10) do
    Route
    |> order_by([r], desc: r.inserted_at)
    |> limit(^limit)
    |> Repo.all()
  end

  @doc """
  Searches routes by name (case-insensitive).
  """
  def search_routes_by_name(query) when is_binary(query) do
    search_term = "%#{String.downcase(query)}%"
    
    Route
    |> where([r], ilike(r.name, ^search_term))
    |> order_by([r], asc: r.name)
    |> Repo.all()
  end

  @doc """
  Returns routes created by a specific user.
  """
  def list_routes_by_user(user_id) do
    Route
    |> where([r], r.created_by == ^user_id)
    |> order_by([r], desc: r.inserted_at)
    |> Repo.all()
  end

  @doc """
  Marks a route as ready for analysis and returns intersecting bridges.
  """
  def prepare_route_for_analysis(%Route{} = route) do
    with {:ok, route} <- validate_for_analysis(route),
         {:ok, updated_route} <- update_analysis_status(route, "pending") do
      
      intersecting_bridges = find_intersecting_bridges(updated_route)
      
      {:ok, updated_route, intersecting_bridges}
    end
  end

  @doc """
  Cleans up old routes (older than specified days) with no analysis results.
  """
  def cleanup_old_routes(days_old \\ 30) do
    cutoff_date = DateTime.utc_now() |> DateTime.add(-days_old, :day)
    
    Route
    |> where([r], r.inserted_at < ^cutoff_date)
    |> where([r], r.analysis_status == "pending")
    |> join(:left, [r], ar in assoc(r, :analysis_results))
    |> where([r, ar], is_nil(ar.id))
    |> Repo.delete_all()
  end
end
