defmodule Starlight.Vehicles do
  @moduledoc """
  The Vehicles context for managing vehicle data and operations.
  """

  import Ecto.Query, warn: false
  alias Starlight.Repo
  alias Starlight.Vehicles.Vehicle

  @doc """
  Returns the list of vehicles.

  ## Examples

      iex> list_vehicles()
      [%Vehicle{}, ...]

  """
  def list_vehicles do
    Repo.all(Vehicle)
  end

  @doc """
  Returns the list of standard design vehicles.
  """
  def list_standard_vehicles do
    Vehicle
    |> where([v], v.is_standard_vehicle == true)
    |> Repo.all()
  end

  @doc """
  Returns the list of vehicles with complete data for analysis.
  """
  def list_complete_vehicles do
    Vehicle
    |> where([v], not is_nil(v.gross_vehicle_weight) and not is_nil(v.axle_loads) and 
                   not is_nil(v.wheelbase_dimensions))
    |> Repo.all()
    |> Enum.filter(&Vehicle.complete_for_analysis?/1)
  end

  @doc """
  Gets a single vehicle.

  Raises `Ecto.NoResultsError` if the Vehicle does not exist.

  ## Examples

      iex> get_vehicle!(123)
      %Vehicle{}

      iex> get_vehicle!(456)
      ** (Ecto.NoResultsError)

  """
  def get_vehicle!(id), do: Repo.get!(Vehicle, id)

  @doc """
  Gets a single vehicle by ID, returns nil if not found.
  """
  def get_vehicle(id), do: Repo.get(Vehicle, id)

  @doc """
  Creates a vehicle.

  ## Examples

      iex> create_vehicle(%{field: value})
      {:ok, %Vehicle{}}

      iex> create_vehicle(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_vehicle(attrs \\ %{}) do
    %Vehicle{}
    |> Vehicle.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a vehicle.

  ## Examples

      iex> update_vehicle(vehicle, %{field: new_value})
      {:ok, %Vehicle{}}

      iex> update_vehicle(vehicle, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_vehicle(%Vehicle{} = vehicle, attrs) do
    vehicle
    |> Vehicle.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a vehicle.

  ## Examples

      iex> delete_vehicle(vehicle)
      {:ok, %Vehicle{}}

      iex> delete_vehicle(vehicle)
      {:error, %Ecto.Changeset{}}

  """
  def delete_vehicle(%Vehicle{} = vehicle) do
    Repo.delete(vehicle)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking vehicle changes.

  ## Examples

      iex> change_vehicle(vehicle)
      %Ecto.Changeset{data: %Vehicle{}}

  """
  def change_vehicle(%Vehicle{} = vehicle, attrs \\ %{}) do
    Vehicle.changeset(vehicle, attrs)
  end

  @doc """
  Returns vehicles that are missing required data for analysis.
  """
  def list_incomplete_vehicles do
    list_vehicles()
    |> Enum.reject(&Vehicle.complete_for_analysis?/1)
  end

  @doc """
  Validates that a vehicle has all required data for analysis.
  """
  def validate_for_analysis(%Vehicle{} = vehicle) do
    if Vehicle.complete_for_analysis?(vehicle) do
      {:ok, vehicle}
    else
      missing_fields = []
      missing_fields = if is_nil(vehicle.gross_vehicle_weight), 
                         do: ["gross_vehicle_weight" | missing_fields], else: missing_fields
      missing_fields = if is_nil(vehicle.axle_loads) or length(vehicle.axle_loads) == 0, 
                         do: ["axle_loads" | missing_fields], else: missing_fields
      missing_fields = if is_nil(vehicle.wheelbase_dimensions), 
                         do: ["wheelbase_dimensions" | missing_fields], else: missing_fields

      {:error, "Missing required fields: #{Enum.join(missing_fields, ", ")}"}
    end
  end

  @doc """
  Seeds the database with standard design vehicles from Australian standards.
  """
  def seed_standard_vehicles do
    standard_vehicles = [
      %{
        name: "B-Double",
        description: "Standard B-Double truck configuration",
        gross_vehicle_weight: Decimal.new("62.5"),
        axle_loads: [Decimal.new("6.0"), Decimal.new("17.0"), Decimal.new("17.0"), Decimal.new("17.0"), Decimal.new("5.5")],
        wheelbase_dimensions: [Decimal.new("3.7"), Decimal.new("1.3"), Decimal.new("6.0"), Decimal.new("1.3")],
        vehicle_classification: "B_Double",
        vehicle_length: Decimal.new("25.0"),
        vehicle_width: Decimal.new("2.5"),
        vehicle_height: Decimal.new("4.3"),
        is_standard_vehicle: true
      },
      %{
        name: "Road Train (Double)",
        description: "Standard Road Train with two trailers",
        gross_vehicle_weight: Decimal.new("125.0"),
        axle_loads: [Decimal.new("6.0"), Decimal.new("17.0"), Decimal.new("17.0"), Decimal.new("17.0"), 
                     Decimal.new("17.0"), Decimal.new("17.0"), Decimal.new("17.0"), Decimal.new("17.0")],
        wheelbase_dimensions: [Decimal.new("3.7"), Decimal.new("1.3"), Decimal.new("6.0"), Decimal.new("1.3"), 
                               Decimal.new("6.0"), Decimal.new("1.3"), Decimal.new("6.0")],
        vehicle_classification: "Road_Train",
        vehicle_length: Decimal.new("53.5"),
        vehicle_width: Decimal.new("2.5"),
        vehicle_height: Decimal.new("4.3"),
        is_standard_vehicle: true
      },
      %{
        name: "Single Unit Truck",
        description: "Standard single unit rigid truck",
        gross_vehicle_weight: Decimal.new("42.5"),
        axle_loads: [Decimal.new("6.0"), Decimal.new("17.0"), Decimal.new("19.5")],
        wheelbase_dimensions: [Decimal.new("3.2"), Decimal.new("1.3")],
        vehicle_classification: "Rigid_Truck",
        vehicle_length: Decimal.new("12.5"),
        vehicle_width: Decimal.new("2.5"),
        vehicle_height: Decimal.new("4.3"),
        is_standard_vehicle: true
      },
      %{
        name: "Semi-Trailer",
        description: "Standard semi-trailer configuration",
        gross_vehicle_weight: Decimal.new("42.5"),
        axle_loads: [Decimal.new("6.0"), Decimal.new("17.0"), Decimal.new("17.0"), Decimal.new("2.5")],
        wheelbase_dimensions: [Decimal.new("3.7"), Decimal.new("1.3"), Decimal.new("7.8")],
        vehicle_classification: "Semi_Trailer",
        vehicle_length: Decimal.new("19.0"),
        vehicle_width: Decimal.new("2.5"),
        vehicle_height: Decimal.new("4.3"),
        is_standard_vehicle: true
      }
    ]

    Enum.each(standard_vehicles, fn vehicle_attrs ->
      case Repo.get_by(Vehicle, name: vehicle_attrs.name, is_standard_vehicle: true) do
        nil -> create_vehicle(vehicle_attrs)
        _existing -> :ok  # Vehicle already exists
      end
    end)
  end

  @doc """
  Returns vehicles filtered by classification.
  """
  def list_vehicles_by_classification(classification) do
    Vehicle
    |> where([v], v.vehicle_classification == ^classification)
    |> Repo.all()
  end

  @doc """
  Returns vehicles that require permits for the given route restrictions.
  """
  def list_vehicles_requiring_permits(route_restrictions \\ []) do
    Vehicle
    |> where([v], v.permit_required == true)
    |> Repo.all()
    |> Enum.filter(fn vehicle ->
      vehicle.route_restrictions
      |> Kernel.||([])
      |> Enum.any?(&(&1 in route_restrictions))
    end)
  end
end
