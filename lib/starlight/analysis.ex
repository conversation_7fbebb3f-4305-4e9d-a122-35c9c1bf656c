defmodule Starlight.Analysis do
  @moduledoc """
  The Analysis context for managing bridge analysis operations and results.

  This context integrates with the existing ThreeMomentAnalyzer to perform
  structural analysis of bridges under vehicle loading.
  """

  import Ecto.Query, warn: false
  alias Starlight.Repo
  alias Starlight.Analysis.AnalysisResult
  alias Starlight.{Bridges, Vehicles, Routes}
  alias Starlight.ThreeMomentAnalyzer

  @doc """
  Returns the list of analysis results.
  """
  def list_analysis_results do
    AnalysisResult
    |> preload([:bridge, :vehicle, :route, :calculated_by])
    |> Repo.all()
  end

  @doc """
  Returns analysis results for a specific bridge.
  """
  def list_results_for_bridge(bridge_id) do
    AnalysisResult
    |> where([ar], ar.bridge_id == ^bridge_id)
    |> preload([:vehicle, :route, :calculated_by])
    |> order_by([ar], desc: ar.calculated_at)
    |> Repo.all()
  end

  @doc """
  Returns analysis results for a specific route.
  """
  def list_results_for_route(route_id) do
    AnalysisResult
    |> where([ar], ar.route_id == ^route_id)
    |> preload([:bridge, :vehicle, :calculated_by])
    |> order_by([ar], desc: ar.calculated_at)
    |> Repo.all()
  end

  @doc """
  Gets a single analysis result.
  """
  def get_analysis_result!(id) do
    AnalysisResult
    |> preload([:bridge, :vehicle, :route, :calculated_by])
    |> Repo.get!(id)
  end

  @doc """
  Gets a single analysis result by ID, returns nil if not found.
  """
  def get_analysis_result(id) do
    AnalysisResult
    |> preload([:bridge, :vehicle, :route, :calculated_by])
    |> Repo.get(id)
  end

  @doc """
  Creates an analysis result.
  """
  def create_analysis_result(attrs \\ %{}) do
    %AnalysisResult{}
    |> AnalysisResult.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Deletes an analysis result.
  """
  def delete_analysis_result(%AnalysisResult{} = analysis_result) do
    Repo.delete(analysis_result)
  end

  @doc """
  Performs three moment analysis for a bridge-vehicle-route combination.

  This is the main analysis function that integrates with the existing
  ThreeMomentAnalyzer module.
  """
  def analyze_bridge_vehicle_route(bridge_id, vehicle_id, route_id, user_id \\ nil) do
    with {:ok, bridge} <- get_and_validate_bridge(bridge_id),
         {:ok, vehicle} <- get_and_validate_vehicle(vehicle_id),
         {:ok, route} <- get_and_validate_route(route_id),
         {:ok, bridge_data} <- Bridges.to_analysis_format(bridge),
         {:ok, vehicle_data} <- Vehicles.Vehicle.to_analysis_format(vehicle) do

      # Update route status to analyzing
      Routes.update_analysis_status(route, "analyzing")

      # Perform the three moment analysis
      case perform_three_moment_analysis(bridge_data, vehicle_data) do
        {:ok, analysis_results} ->
          # Create and save analysis result
          changeset = AnalysisResult.from_three_moment_analysis(
            bridge_id, vehicle_id, route_id, analysis_results, user_id
          )

          case Repo.insert(changeset) do
            {:ok, result} ->
              # Update route status to completed
              Routes.update_analysis_status(route, "completed")
              {:ok, result}
            {:error, changeset} ->
              Routes.update_analysis_status(route, "error")
              {:error, changeset}
          end

        {:error, reason} ->
          Routes.update_analysis_status(route, "error")
          {:error, "Analysis failed: #{reason}"}
      end
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp get_and_validate_bridge(bridge_id) do
    case Bridges.get_bridge(bridge_id) do
      nil -> {:error, "Bridge not found"}
      bridge -> Bridges.validate_for_analysis(bridge)
    end
  end

  defp get_and_validate_vehicle(vehicle_id) do
    case Vehicles.get_vehicle(vehicle_id) do
      nil -> {:error, "Vehicle not found"}
      vehicle -> Vehicles.validate_for_analysis(vehicle)
    end
  end

  defp get_and_validate_route(route_id) do
    case Routes.get_route(route_id) do
      nil -> {:error, "Route not found"}
      route -> Routes.validate_for_analysis(route)
    end
  end

  defp perform_three_moment_analysis(bridge_data, vehicle_data) do
    # Convert data to format expected by ThreeMomentAnalyzer
    params = %{
      span_lengths: bridge_data.span_lengths,
      moments_of_inertia: bridge_data.moments_of_inertia,
      elastic_modulus: bridge_data.elastic_modulus,
      distributed_loads: List.duplicate(0.0, length(bridge_data.span_lengths)),  # No distributed loads for vehicle analysis
      point_loads: vehicle_data.point_loads,
      point_positions: vehicle_data.point_positions
    }

    # Call the existing three moment analyzer
    ThreeMomentAnalyzer.analyze_beam(
      params.span_lengths,
      params.moments_of_inertia,
      params.elastic_modulus,
      params.distributed_loads,
      params.point_loads,
      params.point_positions
    )
  end

  @doc """
  Analyzes all bridges on a route for a given vehicle.

  Returns a list of analysis results for each bridge intersection.
  """
  def analyze_route(route_id, vehicle_id, user_id \\ nil) do
    with {:ok, route} <- get_and_validate_route(route_id),
         {:ok, _vehicle} <- get_and_validate_vehicle(vehicle_id) do

      # Find all bridges that intersect with the route
      intersecting_bridges = Routes.find_intersecting_bridges(route)

      # Filter bridges that have complete data for analysis
      complete_bridges = Enum.filter(intersecting_bridges, &Bridges.Bridge.complete_for_analysis?/1)

      if Enum.empty?(complete_bridges) do
        {:error, "No bridges with complete data found on route"}
      else
        # Analyze each bridge
        results = Enum.map(complete_bridges, fn bridge ->
          analyze_bridge_vehicle_route(bridge.id, vehicle_id, route_id, user_id)
        end)

        # Separate successful and failed analyses
        {successes, failures} = Enum.split_with(results, fn
          {:ok, _} -> true
          {:error, _} -> false
        end)

        successful_results = Enum.map(successes, fn {:ok, result} -> result end)

        {:ok, %{
          successful_analyses: successful_results,
          failed_analyses: failures,
          total_bridges: length(intersecting_bridges),
          complete_bridges: length(complete_bridges),
          analyzed_bridges: length(successful_results)
        }}
      end
    end
  end

  @doc """
  Returns analysis statistics.
  """
  def analysis_statistics do
    total_results = Repo.aggregate(AnalysisResult, :count, :id)
    passing_results = AnalysisResult |> where([ar], ar.pass_fail_status == true) |> Repo.aggregate(:count, :id)
    failing_results = total_results - passing_results

    recent_results = AnalysisResult
    |> where([ar], ar.calculated_at > ago(7, "day"))
    |> Repo.aggregate(:count, :id)

    %{
      total_analyses: total_results,
      passing_analyses: passing_results,
      failing_analyses: failing_results,
      pass_rate: if(total_results > 0, do: Float.round(passing_results / total_results * 100, 1), else: 0.0),
      recent_analyses: recent_results
    }
  end

  @doc """
  Returns recent analysis results (limit 20).
  """
  def recent_analysis_results(limit \\ 20) do
    AnalysisResult
    |> preload([:bridge, :vehicle, :route, :calculated_by])
    |> order_by([ar], desc: ar.calculated_at)
    |> limit(^limit)
    |> Repo.all()
  end

  @doc """
  Returns analysis results that indicate structural failure.
  """
  def list_failing_analyses do
    AnalysisResult
    |> where([ar], ar.pass_fail_status == false)
    |> preload([:bridge, :vehicle, :route, :calculated_by])
    |> order_by([ar], desc: ar.calculated_at)
    |> Repo.all()
  end

  @doc """
  Finds the most critical analysis result (lowest safety factor) for a bridge.
  """
  def most_critical_result_for_bridge(bridge_id) do
    AnalysisResult
    |> where([ar], ar.bridge_id == ^bridge_id)
    |> where([ar], not is_nil(ar.overall_safety_factor))
    |> order_by([ar], asc: ar.overall_safety_factor)
    |> preload([:vehicle, :route, :calculated_by])
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Cleans up old analysis results (older than specified days).
  """
  def cleanup_old_results(days_old \\ 90) do
    cutoff_date = DateTime.utc_now() |> DateTime.add(-days_old, :day)

    AnalysisResult
    |> where([ar], ar.calculated_at < ^cutoff_date)
    |> Repo.delete_all()
  end
end
