defmodule Starlight.Routes.Route do
  @moduledoc """
  Route schema for storing route geometry and analysis status.

  This schema supports both PostGIS LINESTRING geometry and fallback
  coordinate storage for maximum compatibility.
  """

  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  @analysis_statuses ~w(pending analyzing completed error)

  schema "routes" do
    field :name, :string
    field :description, :string

    # Route geometry - PostGIS LINESTRING with fallback
    # field :geometry, Geo.PostGIS.Geometry  # Disabled until PostGIS is available
    field :coordinates, :map  # Fallback: GeoJSON-like structure

    # Route properties
    field :total_length, :decimal  # meters
    field :start_location, :string
    field :end_location, :string

    # Analysis status
    field :analysis_status, :string, default: "pending"
    field :last_analyzed_at, :utc_datetime

    # Relationships
    belongs_to :created_by_user, Starlight.Accounts.User, foreign_key: :created_by, type: :id
    has_many :analysis_results, Starlight.Analysis.AnalysisResult

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(route, attrs) do
    route
    |> cast(attrs, [
      :name, :description, :geometry, :coordinates, :total_length,
      :start_location, :end_location, :analysis_status, :last_analyzed_at, :created_by
    ])
    |> validate_required([:name])
    |> validate_geometry()
    |> validate_analysis_status()
    |> calculate_total_length()
  end

  defp validate_geometry(changeset) do
    # geometry = get_field(changeset, :geometry)  # Disabled until PostGIS is available
    coordinates = get_field(changeset, :coordinates)

    cond do
      # geometry != nil -> changeset  # Disabled until PostGIS is available
      coordinates != nil -> validate_coordinates_format(changeset)
      true -> add_error(changeset, :coordinates, "Coordinates must be provided")
    end
  end

  defp validate_coordinates_format(changeset) do
    validate_change(changeset, :coordinates, fn :coordinates, coords ->
      case coords do
        %{"type" => "LineString", "coordinates" => coord_list} when is_list(coord_list) ->
          if valid_coordinate_list?(coord_list), do: [], else: [coordinates: "Invalid coordinate format"]
        _ ->
          [coordinates: "Coordinates must be in GeoJSON LineString format"]
      end
    end)
  end

  defp valid_coordinate_list?(coord_list) do
    Enum.all?(coord_list, fn
      [lng, lat] when is_number(lng) and is_number(lat) ->
        lng >= -180 and lng <= 180 and lat >= -90 and lat <= 90
      _ -> false
    end)
  end

  defp validate_analysis_status(changeset) do
    validate_inclusion(changeset, :analysis_status, @analysis_statuses)
  end

  defp calculate_total_length(changeset) do
    # If total_length is not provided, try to calculate it from geometry
    if get_field(changeset, :total_length) == nil do
      # case get_field(changeset, :geometry) do  # Disabled until PostGIS is available
      #   %Geo.LineString{} = linestring ->
      #     length = calculate_linestring_length(linestring)
      #     put_change(changeset, :total_length, Decimal.from_float(length))
      #   _ ->
          case get_field(changeset, :coordinates) do
            %{"coordinates" => coord_list} when is_list(coord_list) ->
              length = calculate_coordinate_length(coord_list)
              put_change(changeset, :total_length, Decimal.from_float(length))
            _ ->
              changeset
          end
      # end
    else
      changeset
    end
  end

  defp calculate_linestring_length(%Geo.LineString{coordinates: coordinates}) do
    calculate_coordinate_length(coordinates)
  end

  defp calculate_coordinate_length(coordinates) when length(coordinates) < 2, do: 0.0
  defp calculate_coordinate_length(coordinates) do
    coordinates
    |> Enum.chunk_every(2, 1, :discard)
    |> Enum.reduce(0.0, fn [[lng1, lat1], [lng2, lat2]], acc ->
      acc + haversine_distance(lat1, lng1, lat2, lng2)
    end)
  end

  # Haversine formula for calculating distance between two points on Earth
  defp haversine_distance(lat1, lng1, lat2, lng2) do
    r = 6371000  # Earth's radius in meters

    lat1_rad = lat1 * :math.pi() / 180
    lat2_rad = lat2 * :math.pi() / 180
    delta_lat = (lat2 - lat1) * :math.pi() / 180
    delta_lng = (lng2 - lng1) * :math.pi() / 180

    a = :math.sin(delta_lat / 2) * :math.sin(delta_lat / 2) +
        :math.cos(lat1_rad) * :math.cos(lat2_rad) *
        :math.sin(delta_lng / 2) * :math.sin(delta_lng / 2)

    c = 2 * :math.atan2(:math.sqrt(a), :math.sqrt(1 - a))

    r * c
  end

  @doc """
  Returns the route coordinates as a list of [lng, lat] pairs.
  Prefers PostGIS geometry, falls back to coordinates field.
  """
  # def get_coordinates(%__MODULE__{geometry: %Geo.LineString{coordinates: coords}}), do: coords  # Disabled until PostGIS is available
  def get_coordinates(%__MODULE__{coordinates: %{"coordinates" => coords}}) when is_list(coords), do: coords
  def get_coordinates(_), do: []

  @doc """
  Returns the start and end points of the route as {lat, lng} tuples.
  """
  def get_endpoints(%__MODULE__{} = route) do
    case get_coordinates(route) do
      [] -> {nil, nil}
      [first | rest] ->
        last = List.last(rest) || first
        {coordinate_to_latlng(first), coordinate_to_latlng(last)}
    end
  end

  defp coordinate_to_latlng([lng, lat]), do: {lat, lng}

  @doc """
  Checks if the route has valid geometry for spatial analysis.
  """
  def has_valid_geometry?(%__MODULE__{} = route) do
    coords = get_coordinates(route)
    length(coords) >= 2
  end

  @doc """
  Updates the analysis status and timestamp.
  """
  def update_analysis_status(%__MODULE__{} = route, status) when status in @analysis_statuses do
    route
    |> change(%{analysis_status: status, last_analyzed_at: DateTime.utc_now() |> DateTime.truncate(:second)})
  end

  @doc """
  Converts route geometry to format suitable for PostGIS spatial queries.
  """
  # def to_postgis_linestring(%__MODULE__{geometry: %Geo.LineString{} = linestring}), do: {:ok, linestring}  # Disabled until PostGIS is available
  def to_postgis_linestring(%__MODULE__{coordinates: %{"coordinates" => coords}}) when is_list(coords) do
    {:ok, %Geo.LineString{coordinates: coords}}
  end
  def to_postgis_linestring(_), do: {:error, "No valid geometry found"}

  @doc """
  Returns a human-readable summary of the route.
  """
  def summary(%__MODULE__{} = route) do
    length_km = if route.total_length do
      Decimal.to_float(route.total_length) / 1000
      |> Float.round(2)
    else
      "unknown"
    end

    coord_count = length(get_coordinates(route))

    "#{route.name}: #{length_km} km, #{coord_count} points, status: #{route.analysis_status}"
  end
end
