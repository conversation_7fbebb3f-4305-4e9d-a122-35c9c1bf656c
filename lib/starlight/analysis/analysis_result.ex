defmodule Starlight.Analysis.AnalysisResult do
  @moduledoc """
  Analysis result schema for storing three moment equation analysis results.

  This schema stores the complete results of bridge analysis including moments,
  reactions, shear forces, stress calculations, and safety factors.
  """

  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  @analysis_methods ~w(three_moment finite_element simplified)

  schema "analysis_results" do
    # Foreign key relationships
    belongs_to :bridge, Starlight.Bridges.Bridge
    belongs_to :vehicle, Starlight.Vehicles.Vehicle
    belongs_to :route, Starlight.Routes.Route
    belongs_to :calculated_by_user, Starlight.Accounts.User, foreign_key: :calculated_by, type: :id

    # Analysis results from three moment equation
    field :moment_values, :map  # JSONB storing moment calculation results
    field :reaction_forces, :map  # JSONB storing reaction force results
    field :shear_forces, :map  # JSONB storing shear force results
    field :deflection_values, :map  # JSONB storing deflection calculations

    # Stress calculations and safety analysis
    field :stress_calculations, :map  # JSONB storing stress analysis results
    field :max_moment, :decimal  # Maximum moment value in N⋅m
    field :max_stress, :decimal  # Maximum stress value in Pa
    field :max_deflection, :decimal  # Maximum deflection in meters

    # Pass/fail analysis
    field :pass_fail_status, :boolean  # Overall pass/fail result
    field :safety_factor_moment, :decimal  # Safety factor for moment
    field :safety_factor_stress, :decimal  # Safety factor for stress
    field :safety_factor_deflection, :decimal  # Safety factor for deflection
    field :overall_safety_factor, :decimal  # Minimum safety factor

    # Analysis metadata
    field :analysis_method, :string, default: "three_moment"
    field :calculation_notes, :string
    field :calculated_at, :utc_datetime

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(analysis_result, attrs) do
    analysis_result
    |> cast(attrs, [
      :bridge_id, :vehicle_id, :route_id, :calculated_by,
      :moment_values, :reaction_forces, :shear_forces, :deflection_values,
      :stress_calculations, :max_moment, :max_stress, :max_deflection,
      :pass_fail_status, :safety_factor_moment, :safety_factor_stress,
      :safety_factor_deflection, :overall_safety_factor,
      :analysis_method, :calculation_notes, :calculated_at
    ])
    |> validate_required([
      :bridge_id, :vehicle_id, :route_id, :moment_values, :reaction_forces,
      :shear_forces, :pass_fail_status, :calculated_at
    ])
    |> validate_analysis_method()
    |> validate_safety_factors()
    |> validate_result_data()
    |> foreign_key_constraint(:bridge_id)
    |> foreign_key_constraint(:vehicle_id)
    |> foreign_key_constraint(:route_id)
    |> foreign_key_constraint(:calculated_by)
  end

  defp validate_analysis_method(changeset) do
    validate_inclusion(changeset, :analysis_method, @analysis_methods)
  end

  defp validate_safety_factors(changeset) do
    changeset
    |> validate_optional_positive(:safety_factor_moment, "Safety factor for moment must be positive")
    |> validate_optional_positive(:safety_factor_stress, "Safety factor for stress must be positive")
    |> validate_optional_positive(:safety_factor_deflection, "Safety factor for deflection must be positive")
    |> validate_optional_positive(:overall_safety_factor, "Overall safety factor must be positive")
  end

  defp validate_optional_positive(changeset, field, message) do
    validate_change(changeset, field, fn ^field, value ->
      if is_nil(value) or Decimal.positive?(value) do
        []
      else
        [{field, message}]
      end
    end)
  end

  defp validate_result_data(changeset) do
    changeset
    |> validate_json_structure(:moment_values, ["moments", "x_coordinates"])
    |> validate_json_structure(:reaction_forces, ["reactions"])
    |> validate_json_structure(:shear_forces, ["shear_forces"])
  end

  defp validate_json_structure(changeset, field, required_keys) do
    validate_change(changeset, field, fn ^field, value ->
      case value do
        map when is_map(map) ->
          missing_keys = Enum.reject(required_keys, &Map.has_key?(map, &1))
          if Enum.empty?(missing_keys) do
            []
          else
            [{field, "Missing required keys: #{Enum.join(missing_keys, ", ")}"}]
          end
        _ ->
          [{field, "Must be a valid JSON object"}]
      end
    end)
  end

  @doc """
  Creates a new analysis result from three moment analyzer output.
  """
  def from_three_moment_analysis(bridge_id, vehicle_id, route_id, analysis_results, user_id \\ nil) do
    %__MODULE__{}
    |> changeset(%{
      bridge_id: bridge_id,
      vehicle_id: vehicle_id,
      route_id: route_id,
      calculated_by: user_id,
      moment_values: format_moment_values(analysis_results),
      reaction_forces: format_reaction_forces(analysis_results),
      shear_forces: format_shear_forces(analysis_results),
      deflection_values: format_deflection_values(analysis_results),
      max_moment: extract_max_moment(analysis_results),
      max_deflection: analysis_results.max_deflection,
      pass_fail_status: determine_pass_fail(analysis_results),
      analysis_method: "three_moment",
      calculated_at: DateTime.utc_now() |> DateTime.truncate(:second)
    })
  end

  defp format_moment_values(results) do
    %{
      "moments" => Nx.to_list(results.moments),
      "x_coordinates" => Nx.to_list(results.x_coordinates),
      "moment_diagram" => Nx.to_list(results.moment_diagram)
    }
  end

  defp format_reaction_forces(results) do
    %{
      "reactions" => Nx.to_list(results.reactions)
    }
  end

  defp format_shear_forces(results) do
    %{
      "shear_forces" => Nx.to_list(results.shear_forces),
      "shear_diagram" => Nx.to_list(results.shear_diagram)
    }
  end

  defp format_deflection_values(results) do
    %{
      "deflection_diagram" => Nx.to_list(results.deflection_diagram),
      "max_deflection" => results.max_deflection
    }
  end

  defp extract_max_moment(results) do
    results.moment_diagram
    |> Nx.abs()
    |> Nx.reduce_max()
    |> Nx.to_number()
    |> Decimal.from_float()
  end

  defp determine_pass_fail(results) do
    # Simple pass/fail based on deflection limits
    # This should be enhanced with proper engineering criteria
    max_deflection = abs(results.max_deflection)
    max_deflection < 0.05  # 5cm deflection limit as example
  end

  @doc """
  Returns a summary of the analysis results.
  """
  def summary(%__MODULE__{} = result) do
    status = if result.pass_fail_status, do: "PASS", else: "FAIL"

    max_moment_str = if result.max_moment do
      "#{Decimal.to_string(result.max_moment)} N⋅m"
    else
      "N/A"
    end

    max_deflection_str = if result.max_deflection do
      "#{Float.round(result.max_deflection * 1000, 2)} mm"
    else
      "N/A"
    end

    "Analysis #{status}: Max moment #{max_moment_str}, Max deflection #{max_deflection_str}"
  end

  @doc """
  Returns the moment diagram data for plotting.
  """
  def moment_diagram_data(%__MODULE__{moment_values: %{"x_coordinates" => x, "moment_diagram" => y}}) do
    Enum.zip(x, y)
  end
  def moment_diagram_data(_), do: []

  @doc """
  Returns the shear diagram data for plotting.
  """
  def shear_diagram_data(%__MODULE__{shear_forces: %{"shear_diagram" => y}, moment_values: %{"x_coordinates" => x}}) do
    Enum.zip(x, y)
  end
  def shear_diagram_data(_), do: []

  @doc """
  Returns the deflection diagram data for plotting.
  """
  def deflection_diagram_data(%__MODULE__{deflection_values: %{"deflection_diagram" => y}, moment_values: %{"x_coordinates" => x}}) do
    Enum.zip(x, y)
  end
  def deflection_diagram_data(_), do: []

  @doc """
  Checks if the analysis result indicates a structural failure.
  """
  def indicates_failure?(%__MODULE__{pass_fail_status: false}), do: true
  def indicates_failure?(%__MODULE__{overall_safety_factor: sf}) when not is_nil(sf) do
    Decimal.compare(sf, Decimal.new("1.0")) == :lt
  end
  def indicates_failure?(_), do: false
end
