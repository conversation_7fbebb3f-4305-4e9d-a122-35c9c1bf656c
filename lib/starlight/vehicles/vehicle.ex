defmodule Starlight.Vehicles.Vehicle do
  @moduledoc """
  Vehicle schema for storing vehicle specifications and load data.

  This schema stores vehicle information needed for bridge analysis including
  axle loads, wheelbase dimensions, and classification data.
  """

  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  @vehicle_classifications ~w(
    Single_Unit B_Double Road_Train Semi_Trailer Truck_Trailer
    Bus Coach Rigid_Truck Articulated_Truck Special_Vehicle
  )

  schema "vehicles" do
    field :name, :string
    field :description, :string

    # Vehicle specifications for analysis
    field :gross_vehicle_weight, :decimal  # Total weight in kN
    field :axle_loads, {:array, :decimal}  # Individual axle loads in kN
    field :wheelbase_dimensions, {:array, :decimal}  # Distances between axles in meters
    field :vehicle_classification, :string

    # Physical dimensions
    field :vehicle_length, :decimal  # meters
    field :vehicle_width, :decimal   # meters
    field :vehicle_height, :decimal  # meters

    # Regulatory information
    field :permit_required, :boolean, default: false
    field :route_restrictions, {:array, :string}

    # Metadata
    field :is_standard_vehicle, :boolean, default: false

    # Relationships
    belongs_to :created_by_user, Starlight.Accounts.User, foreign_key: :created_by, type: :id
    has_many :analysis_results, Starlight.Analysis.AnalysisResult

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(vehicle, attrs) do
    vehicle
    |> cast(attrs, [
      :name, :description, :gross_vehicle_weight, :axle_loads, :wheelbase_dimensions,
      :vehicle_classification, :vehicle_length, :vehicle_width, :vehicle_height,
      :permit_required, :route_restrictions, :is_standard_vehicle, :created_by
    ])
    |> validate_required([:name, :gross_vehicle_weight, :axle_loads, :wheelbase_dimensions, :vehicle_classification])
    |> validate_vehicle_data()
    |> validate_classification()
    |> validate_dimensions()
  end

  defp validate_vehicle_data(changeset) do
    changeset
    |> validate_positive_decimal(:gross_vehicle_weight, "Gross vehicle weight must be positive")
    |> validate_positive_array(:axle_loads, "All axle loads must be positive")
    |> validate_positive_array(:wheelbase_dimensions, "All wheelbase dimensions must be positive")
    |> validate_axle_consistency()
    |> validate_weight_consistency()
  end

  defp validate_positive_decimal(changeset, field, message) do
    validate_change(changeset, field, fn ^field, value ->
      if Decimal.positive?(value), do: [], else: [{field, message}]
    end)
  end

  defp validate_positive_array(changeset, field, message) do
    validate_change(changeset, field, fn ^field, values ->
      if Enum.all?(values, &Decimal.positive?/1) do
        []
      else
        [{field, message}]
      end
    end)
  end

  defp validate_axle_consistency(changeset) do
    axle_loads = get_field(changeset, :axle_loads) || []
    wheelbase_dimensions = get_field(changeset, :wheelbase_dimensions) || []

    # Number of wheelbase dimensions should be one less than number of axles
    expected_wheelbase_count = max(0, length(axle_loads) - 1)
    actual_wheelbase_count = length(wheelbase_dimensions)

    if actual_wheelbase_count == expected_wheelbase_count do
      changeset
    else
      add_error(changeset, :wheelbase_dimensions,
        "Must have #{expected_wheelbase_count} wheelbase dimensions for #{length(axle_loads)} axles")
    end
  end

  defp validate_weight_consistency(changeset) do
    gross_weight = get_field(changeset, :gross_vehicle_weight)
    axle_loads = get_field(changeset, :axle_loads) || []

    if gross_weight && length(axle_loads) > 0 do
      total_axle_weight = Enum.reduce(axle_loads, Decimal.new(0), &Decimal.add/2)

      # Allow for small rounding differences (within 1%)
      tolerance = Decimal.mult(gross_weight, Decimal.new("0.01"))
      difference = Decimal.abs(Decimal.sub(gross_weight, total_axle_weight))

      if Decimal.compare(difference, tolerance) == :lt do
        changeset
      else
        add_error(changeset, :axle_loads,
          "Sum of axle loads (#{total_axle_weight} kN) should approximately equal gross vehicle weight (#{gross_weight} kN)")
      end
    else
      changeset
    end
  end

  defp validate_classification(changeset) do
    validate_inclusion(changeset, :vehicle_classification, @vehicle_classifications)
  end

  defp validate_dimensions(changeset) do
    changeset
    |> validate_optional_positive(:vehicle_length, "Vehicle length must be positive")
    |> validate_optional_positive(:vehicle_width, "Vehicle width must be positive")
    |> validate_optional_positive(:vehicle_height, "Vehicle height must be positive")
  end

  defp validate_optional_positive(changeset, field, message) do
    validate_change(changeset, field, fn ^field, value ->
      if is_nil(value) or Decimal.positive?(value) do
        []
      else
        [{field, message}]
      end
    end)
  end

  @doc """
  Returns the total number of axles for this vehicle.
  """
  def axle_count(%__MODULE__{axle_loads: axle_loads}) when is_list(axle_loads) do
    length(axle_loads)
  end
  def axle_count(_), do: 0

  @doc """
  Returns the total vehicle length based on wheelbase dimensions.
  """
  def calculated_length(%__MODULE__{wheelbase_dimensions: dimensions}) when is_list(dimensions) do
    Enum.reduce(dimensions, Decimal.new(0), &Decimal.add/2)
  end
  def calculated_length(_), do: Decimal.new(0)

  @doc """
  Checks if the vehicle has complete data for analysis.
  """
  def complete_for_analysis?(%__MODULE__{} = vehicle) do
    required_fields = [
      vehicle.gross_vehicle_weight,
      vehicle.axle_loads,
      vehicle.wheelbase_dimensions
    ]

    Enum.all?(required_fields, &(&1 != nil)) and
      length(vehicle.axle_loads) > 0 and
      length(vehicle.wheelbase_dimensions) == length(vehicle.axle_loads) - 1
  end

  @doc """
  Converts vehicle data to the format expected by the three moment analyzer.
  Returns a map with point loads and their positions.
  """
  def to_analysis_format(%__MODULE__{} = vehicle) do
    if complete_for_analysis?(vehicle) do
      # Convert axle loads to point loads
      point_loads = Enum.map(vehicle.axle_loads, &Decimal.to_float/1)

      # Calculate axle positions from wheelbase dimensions
      positions = calculate_axle_positions(vehicle.wheelbase_dimensions)

      {:ok, %{
        point_loads: point_loads,
        point_positions: positions,
        total_weight: Decimal.to_float(vehicle.gross_vehicle_weight)
      }}
    else
      {:error, "Vehicle data incomplete for analysis"}
    end
  end

  defp calculate_axle_positions(wheelbase_dimensions) do
    # First axle at position 0, subsequent axles at cumulative distances
    {_, positions} = Enum.reduce(wheelbase_dimensions, {0.0, [0.0]}, fn distance, {current_pos, acc} ->
      new_pos = current_pos + Decimal.to_float(distance)
      {new_pos, [new_pos | acc]}
    end)

    Enum.reverse(positions)
  end
end
