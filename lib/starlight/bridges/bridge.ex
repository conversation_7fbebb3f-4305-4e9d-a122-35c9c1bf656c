defmodule Starlight.Bridges.Bridge do
  @moduledoc """
  Bridge schema for storing bridge structural information and location data.

  This schema supports both PostGIS geometry fields and fallback lat/lng coordinates
  for maximum compatibility.
  """

  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  @support_conditions ~w(fixed pinned roller)
  @condition_ratings ~w(excellent good fair poor)

  schema "bridges" do
    field :name, :string
    field :description, :string

    # Location fields - PostGIS geometry with lat/lng fallback
    # field :location, Geo.PostGIS.Geometry  # Disabled until PostGIS is available
    field :latitude, :decimal
    field :longitude, :decimal

    # Structural properties for three moment analysis
    field :span_lengths, {:array, :decimal}
    field :support_conditions, {:array, :string}
    field :elastic_modulus, :decimal  # E in GPa
    field :moment_of_inertia, {:array, :decimal}  # I in m^4 for each span

    # Load ratings and operational data
    field :load_rating, :decimal  # kN
    field :year_built, :integer
    field :last_inspection, :date
    field :condition_rating, :string

    # Relationships
    belongs_to :created_by_user, Starlight.Accounts.User, foreign_key: :created_by, type: :id
    has_many :analysis_results, Starlight.Analysis.AnalysisResult

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(bridge, attrs) do
    bridge
    |> cast(attrs, [
      :name, :description, :location, :latitude, :longitude,
      :span_lengths, :support_conditions, :elastic_modulus, :moment_of_inertia,
      :load_rating, :year_built, :last_inspection, :condition_rating, :created_by
    ])
    |> validate_required([:name, :span_lengths, :support_conditions, :elastic_modulus, :moment_of_inertia])
    |> validate_location()
    |> validate_structural_data()
    |> validate_condition_rating()
    |> validate_support_conditions()
  end

  defp validate_location(changeset) do
    # Require either PostGIS geometry or lat/lng coordinates
    location = get_field(changeset, :location)
    latitude = get_field(changeset, :latitude)
    longitude = get_field(changeset, :longitude)

    cond do
      location != nil -> changeset
      latitude != nil and longitude != nil -> changeset
      true -> add_error(changeset, :location, "Either geometry or latitude/longitude must be provided")
    end
  end

  defp validate_structural_data(changeset) do
    span_lengths = get_field(changeset, :span_lengths) || []
    support_conditions = get_field(changeset, :support_conditions) || []
    moment_of_inertia = get_field(changeset, :moment_of_inertia) || []

    changeset
    |> validate_length(:span_lengths, min: 1, message: "At least one span is required")
    |> validate_positive_values(:span_lengths, "Span lengths must be positive")
    |> validate_positive_values(:moment_of_inertia, "Moment of inertia values must be positive")
    |> validate_change(:elastic_modulus, fn :elastic_modulus, value ->
      if Decimal.positive?(value), do: [], else: [elastic_modulus: "must be positive"]
    end)
    |> validate_support_count(span_lengths, support_conditions)
    |> validate_moment_inertia_count(span_lengths, moment_of_inertia)
  end

  defp validate_positive_values(changeset, field, message) do
    validate_change(changeset, field, fn ^field, values ->
      if Enum.all?(values, &Decimal.positive?/1) do
        []
      else
        [{field, message}]
      end
    end)
  end

  defp validate_support_count(changeset, span_lengths, support_conditions) do
    expected_supports = length(span_lengths) + 1
    actual_supports = length(support_conditions)

    if actual_supports == expected_supports do
      changeset
    else
      add_error(changeset, :support_conditions,
        "Must have #{expected_supports} support conditions for #{length(span_lengths)} spans")
    end
  end

  defp validate_moment_inertia_count(changeset, span_lengths, moment_of_inertia) do
    expected_count = length(span_lengths)
    actual_count = length(moment_of_inertia)

    if actual_count == expected_count do
      changeset
    else
      add_error(changeset, :moment_of_inertia,
        "Must have #{expected_count} moment of inertia values for #{length(span_lengths)} spans")
    end
  end

  defp validate_condition_rating(changeset) do
    validate_inclusion(changeset, :condition_rating, @condition_ratings)
  end

  defp validate_support_conditions(changeset) do
    validate_change(changeset, :support_conditions, fn :support_conditions, conditions ->
      invalid_conditions = Enum.reject(conditions, &(&1 in @support_conditions))

      if Enum.empty?(invalid_conditions) do
        []
      else
        [support_conditions: "Invalid support conditions: #{Enum.join(invalid_conditions, ", ")}. Must be one of: #{Enum.join(@support_conditions, ", ")}"]
      end
    end)
  end

  @doc """
  Returns the coordinates of the bridge as a {lat, lng} tuple.
  Prefers PostGIS geometry, falls back to lat/lng fields.
  """
  # def coordinates(%__MODULE__{location: %Geo.Point{coordinates: {lng, lat}}}), do: {lat, lng}
  def coordinates(%__MODULE__{latitude: lat, longitude: lng}) when not is_nil(lat) and not is_nil(lng) do
    {Decimal.to_float(lat), Decimal.to_float(lng)}
  end
  def coordinates(_), do: nil

  @doc """
  Checks if the bridge has complete structural data for analysis.
  """
  def complete_for_analysis?(%__MODULE__{} = bridge) do
    required_fields = [
      bridge.span_lengths,
      bridge.support_conditions,
      bridge.elastic_modulus,
      bridge.moment_of_inertia
    ]

    Enum.all?(required_fields, &(&1 != nil)) and
      length(bridge.span_lengths) > 0 and
      length(bridge.support_conditions) == length(bridge.span_lengths) + 1 and
      length(bridge.moment_of_inertia) == length(bridge.span_lengths)
  end
end
