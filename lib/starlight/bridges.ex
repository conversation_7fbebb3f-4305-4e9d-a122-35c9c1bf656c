defmodule Starlight.Bridges do
  @moduledoc """
  The Bridges context for managing bridge data and operations.
  """

  import Ecto.Query, warn: false
  alias <PERSON><PERSON>.Repo
  alias Starlight.Bridges.Bridge

  @doc """
  Returns the list of bridges.

  ## Examples

      iex> list_bridges()
      [%Bridge{}, ...]

  """
  def list_bridges do
    Repo.all(Bridge)
  end

  @doc """
  Returns the list of bridges with complete structural data for analysis.
  """
  def list_complete_bridges do
    Bridge
    |> where([b], not is_nil(b.span_lengths) and not is_nil(b.support_conditions) and
                   not is_nil(b.elastic_modulus) and not is_nil(b.moment_of_inertia))
    |> Repo.all()
    |> Enum.filter(&Bridge.complete_for_analysis?/1)
  end

  @doc """
  Gets a single bridge.

  Raises `Ecto.NoResultsError` if the Bridge does not exist.

  ## Examples

      iex> get_bridge!(123)
      %Bridge{}

      iex> get_bridge!(456)
      ** (Ecto.NoResultsError)

  """
  def get_bridge!(id), do: Repo.get!(Bridge, id)

  @doc """
  Gets a single bridge by ID, returns nil if not found.
  """
  def get_bridge(id), do: Repo.get(Bridge, id)

  @doc """
  Creates a bridge.

  ## Examples

      iex> create_bridge(%{field: value})
      {:ok, %Bridge{}}

      iex> create_bridge(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_bridge(attrs \\ %{}) do
    %Bridge{}
    |> Bridge.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a bridge.

  ## Examples

      iex> update_bridge(bridge, %{field: new_value})
      {:ok, %Bridge{}}

      iex> update_bridge(bridge, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_bridge(%Bridge{} = bridge, attrs) do
    bridge
    |> Bridge.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a bridge.

  ## Examples

      iex> delete_bridge(bridge)
      {:ok, %Bridge{}}

      iex> delete_bridge(bridge)
      {:error, %Ecto.Changeset{}}

  """
  def delete_bridge(%Bridge{} = bridge) do
    Repo.delete(bridge)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking bridge changes.

  ## Examples

      iex> change_bridge(bridge)
      %Ecto.Changeset{data: %Bridge{}}

  """
  def change_bridge(%Bridge{} = bridge, attrs \\ %{}) do
    Bridge.changeset(bridge, attrs)
  end

  @doc """
  Finds bridges that intersect with a given route geometry.

  This function will use PostGIS spatial queries when available,
  otherwise falls back to coordinate-based spatial analysis.
  """
  def find_bridges_on_route(route) do
    # Use the new spatial module for intersection detection
    Starlight.Spatial.find_bridges_intersecting_route(route)
  end



  @doc """
  Returns bridges that are missing required data for analysis.
  """
  def list_incomplete_bridges do
    list_bridges()
    |> Enum.reject(&Bridge.complete_for_analysis?/1)
  end

  @doc """
  Validates that a bridge has all required data for three moment analysis.
  """
  def validate_for_analysis(%Bridge{} = bridge) do
    if Bridge.complete_for_analysis?(bridge) do
      {:ok, bridge}
    else
      missing_fields = []
      missing_fields = if is_nil(bridge.span_lengths) or length(bridge.span_lengths) == 0,
                         do: ["span_lengths" | missing_fields], else: missing_fields
      missing_fields = if is_nil(bridge.support_conditions),
                         do: ["support_conditions" | missing_fields], else: missing_fields
      missing_fields = if is_nil(bridge.elastic_modulus),
                         do: ["elastic_modulus" | missing_fields], else: missing_fields
      missing_fields = if is_nil(bridge.moment_of_inertia),
                         do: ["moment_of_inertia" | missing_fields], else: missing_fields

      {:error, "Missing required fields: #{Enum.join(missing_fields, ", ")}"}
    end
  end

  @doc """
  Converts bridge structural data to format expected by three moment analyzer.
  """
  def to_analysis_format(%Bridge{} = bridge) do
    case validate_for_analysis(bridge) do
      {:ok, _} ->
        {:ok, %{
          span_lengths: Enum.map(bridge.span_lengths, &Decimal.to_float/1),
          moments_of_inertia: Enum.map(bridge.moment_of_inertia, &Decimal.to_float/1),
          elastic_modulus: Decimal.to_float(bridge.elastic_modulus) * 1.0e9,  # Convert GPa to Pa
          support_conditions: bridge.support_conditions
        }}
      error -> error
    end
  end
end
