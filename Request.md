I want you to implement a comprehensive GIS/mapping solution for bridge engineering analysis using Phoenix LiveView with Leaflet.js integration. This system should leverage existing three moment equation analysis capabilities and use PostGIS with PostgreSQL for geometry storage.

**Project Setup Requirements:**
- Create a new Phoenix LiveView application with PostGIS/PostgreSQL database
- Set up Leaflet.js with the Leaflet.draw plugin for map functionality
- Configure Phoenix PubSub for real-time updates
- Implement proper JavaScript hooks for LiveView-Leaflet integration

**Database Schema & Models (Ecto + PostGIS):**
1. **Bridges table:**
   - PostGIS geometry column for location (POINT)
   - span_lengths (array of decimal values in meters)
   - support_conditions (enum: fixed, pinned, roller for each span)
   - material_properties (E: elastic modulus in GPa, I: moment of inertia in m⁴)
   - load_ratings (decimal in kN)
   - name, description (text fields)

2. **Vehicles table:**
   - gross_vehicle_weight (decimal in kN)
   - axle_loads (array of decimal values in kN)
   - wheelbase_dimensions (array of decimal values in meters)
   - vehicle_classification (string)

3. **Routes table:**
   - PostGIS geometry column (LINESTRING)
   - name, description (text fields)
   - created_at timestamp

4. **Analysis_results table:**
   - bridge_id, vehicle_id, route_id (foreign keys)
   - moment_values (JSONB storing calculation results)
   - stress_calculations (JSONB)
   - pass_fail_status (boolean)
   - safety_factors (decimal)
   - calculated_at timestamp

**Core LiveView Components to Implement:**
1. **MapLive** - Main map interface LiveView
2. **BridgeFormComponent** - Modal form for bridge data entry
3. **VehicleFormComponent** - Modal form for vehicle specifications
4. **AnalysisResultsComponent** - Display calculation results
5. **RouteDrawingComponent** - Handle route creation workflow

**Frontend Implementation (Leaflet.js Integration):**
1. Create JavaScript hooks for:
   - Route drawing with Leaflet.draw polyline tool
   - Bridge marker management with custom icons
   - Real-time intersection detection using PostGIS spatial queries
   - Bidirectional communication with LiveView events

2. Map functionality:
   - Initialize Leaflet map with appropriate tile layers
   - Load existing bridges as interactive markers
   - Implement drawing controls for route creation
   - Handle map events (click, draw:created, draw:edited)
   - Update markers based on analysis results (green/red color coding)

**Backend Logic (Phoenix LiveView + Elixir):**
1. **Intersection Detection:**
   - Use PostGIS ST_Intersects() to find bridges along drawn routes
   - Implement real-time spatial queries as routes are drawn
   - Return intersected bridge IDs to frontend

2. **Data Validation Pipeline:**
   - Check completeness of bridge structural data
   - Validate vehicle specifications before analysis
   - Provide specific error messages for missing required fields

3. **Integration with Existing Three Moment Analysis:**
   - Locate and integrate existing three moment equation implementation
   - DO NOT reimplement the calculation method
   - Ensure proper data format conversion for existing analysis functions
   - Handle various support condition scenarios

**User Workflow Implementation:**
1. Map loads with existing bridge markers displayed
2. User activates drawing mode → enable Leaflet.draw controls
3. As user draws route → real-time PostGIS intersection queries
4. Route completion → validate all intersected bridges have complete data
5. Missing data → highlight affected bridges, disable "Analyze Route" button
6. Bridge data entry → modal forms with validation
7. Complete data → enable analysis button
8. Analysis trigger → vehicle form if needed, then run calculations
9. Results display → update map markers with color coding
10. Detailed results → clickable markers show calculation details

**Technical Specifications:**
- Use Phoenix.LiveView.JS for JavaScript interop
- Implement proper error boundaries and loading states
- Create reusable form components with real-time validation
- Use Phoenix channels for multi-user real-time updates
- Ensure responsive design for mobile/desktop
- Add accessibility features (ARIA labels, keyboard navigation)

**Specific Implementation Steps:**
1. First, examine the existing codebase to locate the three moment equation analysis code
2. Set up the Phoenix LiveView application structure
3. Configure PostGIS database with proper spatial indexes
4. Create Ecto schemas with PostGIS geometry fields
5. Implement the main MapLive LiveView with Leaflet integration
6. Build the JavaScript hooks for map interactions
7. Create form components for data entry
8. Integrate with existing three moment analysis functions
9. Implement the complete user workflow with proper error handling
10. Add comprehensive testing for both frontend and backend functionality

**Success Criteria:**
- Users can draw routes on the map and see real-time bridge intersections
- Missing data is clearly indicated with actionable forms
- Three moment equation analysis runs correctly using existing implementation
- Results are displayed with clear visual feedback
- System handles errors gracefully with helpful user messages
- Code follows Phoenix LiveView best practices and is maintainable

Please start by examining the existing codebase to understand the current three moment analysis implementation, then proceed with the Phoenix LiveView application setup.