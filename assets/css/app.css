@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* Import Leaflet CSS via CDN to avoid build issues */
@import url('https://unpkg.com/leaflet@1.9.4/dist/leaflet.css');
@import url('https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css');

/* Map Styles */
.map-container {
  height: 600px;
  width: 100%;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  /* Ensure the container is always visible */
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Bridge Marker Styles */
.bridge-marker {
  background: transparent;
  border: none;
}

.bridge-icon {
  font-size: 20px;
  text-align: center;
  line-height: 30px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.bridge-marker-red .bridge-icon {
  border-color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
}

.bridge-marker-yellow .bridge-icon {
  border-color: #ffc107;
  background: rgba(255, 193, 7, 0.1);
}

.bridge-marker-green .bridge-icon {
  border-color: #28a745;
  background: rgba(40, 167, 69, 0.1);
}

.bridge-marker-blue .bridge-icon {
  border-color: #007bff;
  background: rgba(0, 123, 255, 0.1);
}

.bridge-marker-gray .bridge-icon {
  border-color: #6c757d;
  background: rgba(108, 117, 125, 0.1);
}

.bridge-marker-highlight .bridge-icon {
  border-color: #ff6b35;
  background: rgba(255, 107, 53, 0.2);
  transform: scale(1.2);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { transform: scale(1.2); }
  50% { transform: scale(1.3); }
  100% { transform: scale(1.2); }
}

/* Popup Styles */
.leaflet-popup-content {
  margin: 8px 12px;
  line-height: 1.4;
}

.bridge-popup h3,
.route-popup h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.bridge-popup p,
.route-popup p {
  margin: 4px 0;
  font-size: 14px;
  color: #666;
}

.bridge-popup .btn,
.route-popup .btn {
  margin-top: 8px;
  padding: 4px 8px;
  font-size: 12px;
  border: none;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
  transition: background 0.2s ease;
}

.bridge-popup .btn:hover,
.route-popup .btn:hover {
  background: #0056b3;
}

/* Drawing Controls */
.leaflet-draw-toolbar {
  margin-top: 10px;
}

.leaflet-draw-toolbar a {
  background-color: #fff;
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.leaflet-draw-toolbar a:hover {
  background-color: #f4f4f4;
}

/* Route Analysis Status Colors */
.route-status-pending { color: #6c757d; }
.route-status-analyzing { color: #ffc107; }
.route-status-completed { color: #28a745; }
.route-status-error { color: #dc3545; }

/* This file is for your main application CSS */
