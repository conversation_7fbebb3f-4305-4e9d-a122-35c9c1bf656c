// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"
// Establish Phoenix Socket and LiveView configuration.
import {Socket} from "phoenix"
import {LiveSocket} from "phoenix_live_view"
import topbar from "../vendor/topbar"

// Import Leaflet CSS - using custom versions to avoid image loading issues
// import "leaflet/dist/leaflet.css"
// import "leaflet-draw/dist/leaflet.draw.css"

// Import hooks
import Hooks from "./hooks"

let csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")
let liveSocket = new LiveSocket("/live", Socket, {
  longPollFallbackMs: 2500,
  params: {_csrf_token: csrfToken},
  hooks: Hooks
})

// Show progress bar on live navigation and form submits
topbar.config({barColors: {0: "#29d"}, shadowColor: "rgba(0, 0, 0, .3)"})
window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

// connect if there are any LiveViews on the page
liveSocket.connect()

// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket

// Expose map debugging functions globally
window.debugMap = function() {
  const mapElement = document.getElementById('map');
  if (mapElement && mapElement.phxHook) {
    const hook = mapElement.phxHook;
    console.log('=== Map Debug Information ===');
    console.log('Map State:', hook.getMapState());
    const issues = hook.diagnoseMapIssues();
    if (issues.length > 0) {
      console.warn('Issues found:', issues);
    } else {
      console.log('No issues detected');
    }
    return hook.getMapState();
  } else {
    console.error('Map hook not found');
    return null;
  }
};

// Helper function to reinitialize map
window.reinitializeMap = function() {
  const mapElement = document.getElementById('map');
  if (mapElement && mapElement.phxHook) {
    const hook = mapElement.phxHook;
    console.log('Reinitializing map...');
    try {
      hook.initializeMap();
      hook.setupEventListeners();
      console.log('Map reinitialized successfully');
    } catch (error) {
      console.error('Failed to reinitialize map:', error);
    }
  } else {
    console.error('Map hook not found');
  }
};

