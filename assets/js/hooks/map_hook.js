import L from 'leaflet'
import 'leaflet-draw'

// Fix for default markers in Leaflet with webpack
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: '/images/marker-icon-2x.png',
  iconUrl: '/images/marker-icon.png',
  shadowUrl: '/images/marker-shadow.png',
});

const MapHook = {
  mounted() {
    console.log('MapHook mounted');
    this.isDestroyed = false;
    this.initializationAttempts = 0;
    this.maxInitializationAttempts = 3;

    try {
      this.initializeMap();
      this.setupEventListeners();
    } catch (error) {
      console.error('Error during MapHook initialization:', error);
      this.retryInitialization();
    }
  },

  destroyed() {
    console.log('MapHook destroyed');
    this.isDestroyed = true;

    if (this.map) {
      try {
        this.map.remove();
        this.map = null;
      } catch (error) {
        console.error('Error during map cleanup:', error);
      }
    }

    // Clear any pending timeouts
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }
  },

  reconnected() {
    console.log('MapHook reconnected');
    if (!this.map || this.isDestroyed) {
      console.log('Reinitializing map after reconnection');
      this.initializeMap();
      this.setupEventListeners();
    }
  },

  retryInitialization() {
    if (this.initializationAttempts < this.maxInitializationAttempts && !this.isDestroyed) {
      this.initializationAttempts++;
      console.log(`Retrying map initialization (attempt ${this.initializationAttempts})`);

      this.retryTimeout = setTimeout(() => {
        try {
          this.initializeMap();
          this.setupEventListeners();
        } catch (error) {
          console.error(`Map initialization retry ${this.initializationAttempts} failed:`, error);
          this.retryInitialization();
        }
      }, 1000 * this.initializationAttempts); // Exponential backoff
    } else {
      console.error('Max initialization attempts reached. Map initialization failed.');
    }
  },

  initializeMap() {
    if (this.isDestroyed) {
      console.log('Skipping map initialization - hook is destroyed');
      return;
    }

    // Ensure the container element exists and is visible
    if (!this.el || !this.el.offsetParent) {
      console.warn('Map container is not visible, retrying...');
      throw new Error('Map container not ready');
    }

    // Clean up existing map if it exists
    if (this.map) {
      try {
        this.map.remove();
      } catch (error) {
        console.warn('Error cleaning up existing map:', error);
      }
    }

    // Get initial configuration from data attributes
    const lat = parseFloat(this.el.dataset.lat) || -25.2744;  // Default to Adelaide, Australia
    const lng = parseFloat(this.el.dataset.lng) || 133.7751;
    const zoom = parseInt(this.el.dataset.zoom) || 6;

    console.log(`Initializing map at [${lat}, ${lng}] with zoom ${zoom}`);

    try {
      // Initialize the map
      this.map = L.map(this.el, {
        center: [lat, lng],
        zoom: zoom,
        zoomControl: true,
        preferCanvas: false,
        attributionControl: true
      });

      // Add tile layer with error handling
      const tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 18,
        errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
      });

      tileLayer.on('tileerror', (error) => {
        console.warn('Tile loading error:', error);
      });

      tileLayer.addTo(this.map);

      // Initialize layer groups
      this.bridgeLayer = L.layerGroup().addTo(this.map);
      this.routeLayer = L.layerGroup().addTo(this.map);
      this.drawnItems = new L.FeatureGroup();
      this.map.addLayer(this.drawnItems);

      // Setup drawing controls
      this.setupDrawingControls();

      // Add map event listeners for debugging
      this.map.on('zoomend moveend', () => {
        if (!this.isDestroyed) {
          console.log('Map view changed:', this.map.getCenter(), this.map.getZoom());
        }
      });

      // Load initial data
      this.loadBridges();
      this.loadRoutes();

      console.log('Map initialized successfully');
    } catch (error) {
      console.error('Error initializing map:', error);
      throw error;
    }
  },

  setupDrawingControls() {
    // Drawing control configuration
    const drawControl = new L.Control.Draw({
      position: 'topright',
      draw: {
        polygon: false,
        circle: false,
        rectangle: false,
        marker: false,
        circlemarker: false,
        polyline: {
          shapeOptions: {
            color: '#3388ff',
            weight: 4,
            opacity: 0.8
          }
        }
      },
      edit: {
        featureGroup: this.drawnItems,
        remove: true
      }
    });

    this.map.addControl(drawControl);

    // Drawing event handlers
    this.map.on(L.Draw.Event.CREATED, (e) => {
      const layer = e.layer;
      this.drawnItems.addLayer(layer);
      
      if (e.layerType === 'polyline') {
        this.handleRouteDrawn(layer);
      }
    });

    this.map.on(L.Draw.Event.EDITED, (e) => {
      const layers = e.layers;
      layers.eachLayer((layer) => {
        if (layer instanceof L.Polyline) {
          this.handleRouteEdited(layer);
        }
      });
    });

    this.map.on(L.Draw.Event.DELETED, (e) => {
      const layers = e.layers;
      layers.eachLayer((layer) => {
        this.handleRouteDeleted(layer);
      });
    });
  },

  setupEventListeners() {
    if (this.isDestroyed) {
      console.log('Skipping event listener setup - hook is destroyed');
      return;
    }

    console.log('Setting up event listeners');

    // Listen for Phoenix events with error handling
    this.handleEvent('load_bridges', (data) => {
      try {
        console.log('Received load_bridges event with', data.bridges?.length || 0, 'bridges');
        this.updateBridges(data.bridges || []);
      } catch (error) {
        console.error('Error handling load_bridges event:', error);
      }
    });

    this.handleEvent('load_routes', (data) => {
      try {
        console.log('Received load_routes event with', data.routes?.length || 0, 'routes');
        this.updateRoutes(data.routes || []);
      } catch (error) {
        console.error('Error handling load_routes event:', error);
      }
    });

    this.handleEvent('highlight_bridge', (bridge) => {
      try {
        console.log('Received highlight_bridge event:', bridge);
        this.highlightBridge(bridge);
      } catch (error) {
        console.error('Error handling highlight_bridge event:', error);
      }
    });

    this.handleEvent('clear_highlights', () => {
      try {
        console.log('Received clear_highlights event');
        this.clearHighlights();
      } catch (error) {
        console.error('Error handling clear_highlights event:', error);
      }
    });

    this.handleEvent('fit_bounds', (bounds) => {
      try {
        if (bounds && bounds.length === 4 && this.map && !this.isDestroyed) {
          console.log('Fitting bounds:', bounds);
          this.map.fitBounds([[bounds[0], bounds[1]], [bounds[2], bounds[3]]]);
        }
      } catch (error) {
        console.error('Error handling fit_bounds event:', error);
      }
    });
  },

  loadBridges() {
    // Request bridge data from LiveView
    this.pushEvent('request_bridges', {});
  },

  loadRoutes() {
    // Request route data from LiveView
    this.pushEvent('request_routes', {});
  },

  updateBridges(bridges) {
    if (!this.map || this.isDestroyed) {
      console.warn('Cannot update bridges - map not initialized');
      return;
    }

    if (!Array.isArray(bridges)) {
      console.warn('Invalid bridges data:', bridges);
      return;
    }

    console.log(`Updating ${bridges.length} bridges on map`);

    try {
      // Clear existing bridge markers
      this.bridgeLayer.clearLayers();

      let validBridges = 0;
      bridges.forEach(bridge => {
        if (bridge.latitude && bridge.longitude) {
          try {
            const marker = this.createBridgeMarker(bridge);
            this.bridgeLayer.addLayer(marker);
            validBridges++;
          } catch (error) {
            console.error('Error creating bridge marker:', bridge, error);
          }
        } else {
          console.warn('Bridge missing coordinates:', bridge);
        }
      });

      console.log(`Successfully added ${validBridges} bridge markers`);
    } catch (error) {
      console.error('Error updating bridges:', error);
    }
  },

  createBridgeMarker(bridge) {
    // Create custom icon based on bridge status
    const iconColor = this.getBridgeIconColor(bridge);
    const icon = L.divIcon({
      className: `bridge-marker bridge-marker-${iconColor}`,
      html: `<div class="bridge-icon">🌉</div>`,
      iconSize: [30, 30],
      iconAnchor: [15, 15]
    });

    const marker = L.marker([bridge.latitude, bridge.longitude], { icon })
      .bindPopup(this.createBridgePopup(bridge));

    // Store bridge data on marker
    marker.bridgeData = bridge;

    // Click handler
    marker.on('click', () => {
      this.pushEvent('bridge_clicked', { bridge_id: bridge.id });
    });

    return marker;
  },

  getBridgeIconColor(bridge) {
    // Color coding based on bridge condition or analysis results
    if (bridge.condition_rating === 'poor') return 'red';
    if (bridge.condition_rating === 'fair') return 'yellow';
    if (bridge.condition_rating === 'good') return 'green';
    if (bridge.condition_rating === 'excellent') return 'blue';
    return 'gray';
  },

  createBridgePopup(bridge) {
    return `
      <div class="bridge-popup">
        <h3>${bridge.name}</h3>
        <p><strong>Condition:</strong> ${bridge.condition_rating || 'Unknown'}</p>
        <p><strong>Spans:</strong> ${bridge.span_count || 'N/A'}</p>
        <p><strong>Load Rating:</strong> ${bridge.load_rating || 'N/A'} kN</p>
        <button class="btn btn-sm btn-primary" onclick="window.liveSocket.pushEvent('view_bridge', {bridge_id: '${bridge.id}'})">
          View Details
        </button>
      </div>
    `;
  },

  updateRoutes(routes) {
    // Clear existing route lines
    this.routeLayer.clearLayers();

    routes.forEach(route => {
      if (route.coordinates && route.coordinates.coordinates) {
        const routeLine = this.createRouteLine(route);
        this.routeLayer.addLayer(routeLine);
      }
    });
  },

  createRouteLine(route) {
    // Convert coordinates to Leaflet format
    const latlngs = route.coordinates.coordinates.map(coord => [coord[1], coord[0]]);
    
    // Color based on analysis status
    const color = this.getRouteColor(route.analysis_status);
    
    const polyline = L.polyline(latlngs, {
      color: color,
      weight: 3,
      opacity: 0.7
    }).bindPopup(this.createRoutePopup(route));

    // Store route data
    polyline.routeData = route;

    // Click handler
    polyline.on('click', () => {
      this.pushEvent('route_clicked', { route_id: route.id });
    });

    return polyline;
  },

  getRouteColor(status) {
    switch (status) {
      case 'completed': return '#28a745';  // Green
      case 'analyzing': return '#ffc107';  // Yellow
      case 'error': return '#dc3545';      // Red
      default: return '#6c757d';           // Gray
    }
  },

  createRoutePopup(route) {
    return `
      <div class="route-popup">
        <h3>${route.name}</h3>
        <p><strong>Status:</strong> ${route.analysis_status}</p>
        <p><strong>Length:</strong> ${route.total_length ? (route.total_length / 1000).toFixed(2) + ' km' : 'N/A'}</p>
        <button class="btn btn-sm btn-primary" onclick="window.liveSocket.pushEvent('analyze_route', {route_id: '${route.id}'})">
          Analyze Route
        </button>
      </div>
    `;
  },

  handleRouteDrawn(layer) {
    // Convert Leaflet coordinates to GeoJSON format
    const coordinates = layer.getLatLngs().map(latlng => [latlng.lng, latlng.lat]);

    // Send to LiveView with real-time intersection detection
    this.pushEvent('route_drawn', {
      coordinates: coordinates,
      type: 'LineString'
    });

    // Perform real-time intersection detection
    this.checkIntersections(coordinates);
  },

  handleRouteEdited(layer) {
    const coordinates = layer.getLatLngs().map(latlng => [latlng.lng, latlng.lat]);
    
    this.pushEvent('route_edited', {
      route_id: layer.routeData?.id,
      coordinates: coordinates,
      type: 'LineString'
    });
  },

  handleRouteDeleted(layer) {
    if (layer.routeData?.id) {
      this.pushEvent('route_deleted', {
        route_id: layer.routeData.id
      });
    }
  },

  highlightBridge(bridge) {
    if (!this.map || this.isDestroyed) {
      console.warn('Cannot highlight bridge - map not initialized');
      return;
    }

    if (!bridge || !bridge.id) {
      console.warn('Invalid bridge data for highlighting:', bridge);
      return;
    }

    console.log('Highlighting bridge:', bridge.name, bridge.id);

    try {
      // Clear existing highlights first
      this.clearHighlights();

      // Find and highlight specific bridge
      let bridgeFound = false;
      this.bridgeLayer.eachLayer(layer => {
        if (layer.bridgeData && layer.bridgeData.id === bridge.id) {
          bridgeFound = true;

          layer.setIcon(L.divIcon({
            className: 'bridge-marker bridge-marker-highlight',
            html: `<div class="bridge-icon highlight">🌉</div>`,
            iconSize: [40, 40],
            iconAnchor: [20, 20]
          }));

          // Open popup
          layer.openPopup();

          // Pan to bridge with validation
          if (bridge.latitude && bridge.longitude) {
            const lat = parseFloat(bridge.latitude);
            const lng = parseFloat(bridge.longitude);

            if (!isNaN(lat) && !isNaN(lng)) {
              this.map.panTo([lat, lng]);
              console.log(`Panned to bridge at [${lat}, ${lng}]`);
            } else {
              console.warn('Invalid bridge coordinates:', bridge.latitude, bridge.longitude);
            }
          }
        }
      });

      if (!bridgeFound) {
        console.warn('Bridge not found in bridge layer:', bridge.id);
        // Try to reload bridges if the bridge wasn't found
        this.loadBridges();
      }
    } catch (error) {
      console.error('Error highlighting bridge:', error);
    }
  },

  clearHighlights() {
    if (!this.map || this.isDestroyed) {
      console.warn('Cannot clear highlights - map not initialized');
      return;
    }

    console.log('Clearing bridge highlights');

    try {
      // Reset all bridge markers to normal state
      this.bridgeLayer.eachLayer(layer => {
        if (layer.bridgeData) {
          const iconColor = this.getBridgeIconColor(layer.bridgeData);
          layer.setIcon(L.divIcon({
            className: `bridge-marker bridge-marker-${iconColor}`,
            html: `<div class="bridge-icon">🌉</div>`,
            iconSize: [30, 30],
            iconAnchor: [15, 15]
          }));
        }
      });
    } catch (error) {
      console.error('Error clearing highlights:', error);
    }
  },

  // Debugging and diagnostic methods
  getMapState() {
    return {
      isDestroyed: this.isDestroyed,
      hasMap: !!this.map,
      mapCenter: this.map ? this.map.getCenter() : null,
      mapZoom: this.map ? this.map.getZoom() : null,
      bridgeCount: this.bridgeLayer ? this.bridgeLayer.getLayers().length : 0,
      routeCount: this.routeLayer ? this.routeLayer.getLayers().length : 0,
      containerVisible: this.el ? !!this.el.offsetParent : false,
      containerSize: this.el ? { width: this.el.offsetWidth, height: this.el.offsetHeight } : null
    };
  },

  diagnoseMapIssues() {
    const state = this.getMapState();
    console.log('Map diagnostic state:', state);

    const issues = [];

    if (state.isDestroyed) issues.push('Hook is destroyed');
    if (!state.hasMap) issues.push('Map not initialized');
    if (!state.containerVisible) issues.push('Container not visible');
    if (state.containerSize && (state.containerSize.width === 0 || state.containerSize.height === 0)) {
      issues.push('Container has zero dimensions');
    }

    if (issues.length > 0) {
      console.warn('Map issues detected:', issues);
      return issues;
    }

    console.log('No map issues detected');
    return [];
  },

  checkIntersections(routeCoordinates) {
    // Simple intersection detection - check if any bridge is within a threshold distance of the route
    const intersectingBridges = [];
    const threshold = 0.01; // ~1km in degrees (rough approximation)

    this.bridgeLayer.eachLayer(layer => {
      if (layer.bridgeData && layer.bridgeData.latitude && layer.bridgeData.longitude) {
        const bridgePoint = [layer.bridgeData.latitude, layer.bridgeData.longitude];

        // Check distance to each route segment
        for (let i = 0; i < routeCoordinates.length - 1; i++) {
          const segmentStart = [routeCoordinates[i][1], routeCoordinates[i][0]]; // lat, lng
          const segmentEnd = [routeCoordinates[i + 1][1], routeCoordinates[i + 1][0]]; // lat, lng

          const distance = this.pointToLineDistance(bridgePoint, segmentStart, segmentEnd);

          if (distance < threshold) {
            intersectingBridges.push(layer.bridgeData);
            // Highlight intersecting bridge
            layer.setIcon(L.divIcon({
              className: 'bridge-marker bridge-marker-highlight',
              html: `<div class="bridge-icon highlight">🌉</div>`,
              iconSize: [35, 35],
              iconAnchor: [17, 17]
            }));
            break;
          }
        }
      }
    });

    // Send intersection data to LiveView
    this.pushEvent('route_intersections', {
      intersecting_bridges: intersectingBridges.map(bridge => bridge.id)
    });
  },

  pointToLineDistance(point, lineStart, lineEnd) {
    // Calculate distance from point to line segment
    const [px, py] = point;
    const [x1, y1] = lineStart;
    const [x2, y2] = lineEnd;

    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;

    if (lenSq === 0) return Math.sqrt(A * A + B * B);

    let param = dot / lenSq;

    let xx, yy;

    if (param < 0) {
      xx = x1;
      yy = y1;
    } else if (param > 1) {
      xx = x2;
      yy = y2;
    } else {
      xx = x1 + param * C;
      yy = y1 + param * D;
    }

    const dx = px - xx;
    const dy = py - yy;

    return Math.sqrt(dx * dx + dy * dy);
  }
};

export default MapHook;
