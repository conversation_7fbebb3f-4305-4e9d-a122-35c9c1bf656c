import L from 'leaflet'
import 'leaflet-draw'

// Fix for default markers in Leaflet with webpack
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: '/images/marker-icon-2x.png',
  iconUrl: '/images/marker-icon.png',
  shadowUrl: '/images/marker-shadow.png',
});

const MapHook = {
  mounted() {
    console.log('MapHook mounted');
    this.initializeMap();
    this.setupEventListeners();
  },

  destroyed() {
    if (this.map) {
      this.map.remove();
    }
  },

  initializeMap() {
    // Get initial configuration from data attributes
    const lat = parseFloat(this.el.dataset.lat) || -25.2744;  // Default to Adelaide, Australia
    const lng = parseFloat(this.el.dataset.lng) || 133.7751;
    const zoom = parseInt(this.el.dataset.zoom) || 6;

    // Initialize the map
    this.map = L.map(this.el, {
      center: [lat, lng],
      zoom: zoom,
      zoomControl: true
    });

    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors',
      maxZoom: 18
    }).addTo(this.map);

    // Initialize layer groups
    this.bridgeLayer = L.layerGroup().addTo(this.map);
    this.routeLayer = L.layerGroup().addTo(this.map);
    this.drawnItems = new L.FeatureGroup();
    this.map.addLayer(this.drawnItems);

    // Setup drawing controls
    this.setupDrawingControls();

    // Load initial data
    this.loadBridges();
    this.loadRoutes();
  },

  setupDrawingControls() {
    // Drawing control configuration
    const drawControl = new L.Control.Draw({
      position: 'topright',
      draw: {
        polygon: false,
        circle: false,
        rectangle: false,
        marker: false,
        circlemarker: false,
        polyline: {
          shapeOptions: {
            color: '#3388ff',
            weight: 4,
            opacity: 0.8
          }
        }
      },
      edit: {
        featureGroup: this.drawnItems,
        remove: true
      }
    });

    this.map.addControl(drawControl);

    // Drawing event handlers
    this.map.on(L.Draw.Event.CREATED, (e) => {
      const layer = e.layer;
      this.drawnItems.addLayer(layer);
      
      if (e.layerType === 'polyline') {
        this.handleRouteDrawn(layer);
      }
    });

    this.map.on(L.Draw.Event.EDITED, (e) => {
      const layers = e.layers;
      layers.eachLayer((layer) => {
        if (layer instanceof L.Polyline) {
          this.handleRouteEdited(layer);
        }
      });
    });

    this.map.on(L.Draw.Event.DELETED, (e) => {
      const layers = e.layers;
      layers.eachLayer((layer) => {
        this.handleRouteDeleted(layer);
      });
    });
  },

  setupEventListeners() {
    // Listen for Phoenix events
    this.handleEvent('load_bridges', (data) => {
      this.updateBridges(data.bridges);
    });

    this.handleEvent('load_routes', (data) => {
      this.updateRoutes(data.routes);
    });

    this.handleEvent('highlight_bridge', (bridge) => {
      this.highlightBridge(bridge);
    });

    this.handleEvent('clear_highlights', () => {
      this.clearHighlights();
    });

    this.handleEvent('fit_bounds', (bounds) => {
      if (bounds && bounds.length === 4) {
        this.map.fitBounds([[bounds[0], bounds[1]], [bounds[2], bounds[3]]]);
      }
    });
  },

  loadBridges() {
    // Request bridge data from LiveView
    this.pushEvent('request_bridges', {});
  },

  loadRoutes() {
    // Request route data from LiveView
    this.pushEvent('request_routes', {});
  },

  updateBridges(bridges) {
    // Clear existing bridge markers
    this.bridgeLayer.clearLayers();

    bridges.forEach(bridge => {
      if (bridge.latitude && bridge.longitude) {
        const marker = this.createBridgeMarker(bridge);
        this.bridgeLayer.addLayer(marker);
      }
    });
  },

  createBridgeMarker(bridge) {
    // Create custom icon based on bridge status
    const iconColor = this.getBridgeIconColor(bridge);
    const icon = L.divIcon({
      className: `bridge-marker bridge-marker-${iconColor}`,
      html: `<div class="bridge-icon">🌉</div>`,
      iconSize: [30, 30],
      iconAnchor: [15, 15]
    });

    const marker = L.marker([bridge.latitude, bridge.longitude], { icon })
      .bindPopup(this.createBridgePopup(bridge));

    // Store bridge data on marker
    marker.bridgeData = bridge;

    // Click handler
    marker.on('click', () => {
      this.pushEvent('bridge_clicked', { bridge_id: bridge.id });
    });

    return marker;
  },

  getBridgeIconColor(bridge) {
    // Color coding based on bridge condition or analysis results
    if (bridge.condition_rating === 'poor') return 'red';
    if (bridge.condition_rating === 'fair') return 'yellow';
    if (bridge.condition_rating === 'good') return 'green';
    if (bridge.condition_rating === 'excellent') return 'blue';
    return 'gray';
  },

  createBridgePopup(bridge) {
    return `
      <div class="bridge-popup">
        <h3>${bridge.name}</h3>
        <p><strong>Condition:</strong> ${bridge.condition_rating || 'Unknown'}</p>
        <p><strong>Spans:</strong> ${bridge.span_count || 'N/A'}</p>
        <p><strong>Load Rating:</strong> ${bridge.load_rating || 'N/A'} kN</p>
        <button class="btn btn-sm btn-primary" onclick="window.liveSocket.pushEvent('view_bridge', {bridge_id: '${bridge.id}'})">
          View Details
        </button>
      </div>
    `;
  },

  updateRoutes(routes) {
    // Clear existing route lines
    this.routeLayer.clearLayers();

    routes.forEach(route => {
      if (route.coordinates && route.coordinates.coordinates) {
        const routeLine = this.createRouteLine(route);
        this.routeLayer.addLayer(routeLine);
      }
    });
  },

  createRouteLine(route) {
    // Convert coordinates to Leaflet format
    const latlngs = route.coordinates.coordinates.map(coord => [coord[1], coord[0]]);
    
    // Color based on analysis status
    const color = this.getRouteColor(route.analysis_status);
    
    const polyline = L.polyline(latlngs, {
      color: color,
      weight: 3,
      opacity: 0.7
    }).bindPopup(this.createRoutePopup(route));

    // Store route data
    polyline.routeData = route;

    // Click handler
    polyline.on('click', () => {
      this.pushEvent('route_clicked', { route_id: route.id });
    });

    return polyline;
  },

  getRouteColor(status) {
    switch (status) {
      case 'completed': return '#28a745';  // Green
      case 'analyzing': return '#ffc107';  // Yellow
      case 'error': return '#dc3545';      // Red
      default: return '#6c757d';           // Gray
    }
  },

  createRoutePopup(route) {
    return `
      <div class="route-popup">
        <h3>${route.name}</h3>
        <p><strong>Status:</strong> ${route.analysis_status}</p>
        <p><strong>Length:</strong> ${route.total_length ? (route.total_length / 1000).toFixed(2) + ' km' : 'N/A'}</p>
        <button class="btn btn-sm btn-primary" onclick="window.liveSocket.pushEvent('analyze_route', {route_id: '${route.id}'})">
          Analyze Route
        </button>
      </div>
    `;
  },

  handleRouteDrawn(layer) {
    // Convert Leaflet coordinates to GeoJSON format
    const coordinates = layer.getLatLngs().map(latlng => [latlng.lng, latlng.lat]);

    // Send to LiveView with real-time intersection detection
    this.pushEvent('route_drawn', {
      coordinates: coordinates,
      type: 'LineString'
    });

    // Perform real-time intersection detection
    this.checkIntersections(coordinates);
  },

  handleRouteEdited(layer) {
    const coordinates = layer.getLatLngs().map(latlng => [latlng.lng, latlng.lat]);
    
    this.pushEvent('route_edited', {
      route_id: layer.routeData?.id,
      coordinates: coordinates,
      type: 'LineString'
    });
  },

  handleRouteDeleted(layer) {
    if (layer.routeData?.id) {
      this.pushEvent('route_deleted', {
        route_id: layer.routeData.id
      });
    }
  },

  highlightBridge(bridge) {
    // Find and highlight specific bridge
    this.bridgeLayer.eachLayer(layer => {
      if (layer.bridgeData && layer.bridgeData.id === bridge.id) {
        layer.setIcon(L.divIcon({
          className: 'bridge-marker bridge-marker-highlight',
          html: `<div class="bridge-icon highlight">🌉</div>`,
          iconSize: [40, 40],
          iconAnchor: [20, 20]
        }));
        
        // Open popup
        layer.openPopup();
        
        // Pan to bridge
        this.map.panTo([bridge.latitude, bridge.longitude]);
      }
    });
  },

  clearHighlights() {
    // Reset all bridge markers to normal state
    this.bridgeLayer.eachLayer(layer => {
      if (layer.bridgeData) {
        const iconColor = this.getBridgeIconColor(layer.bridgeData);
        layer.setIcon(L.divIcon({
          className: `bridge-marker bridge-marker-${iconColor}`,
          html: `<div class="bridge-icon">🌉</div>`,
          iconSize: [30, 30],
          iconAnchor: [15, 15]
        }));
      }
    });
  },

  checkIntersections(routeCoordinates) {
    // Simple intersection detection - check if any bridge is within a threshold distance of the route
    const intersectingBridges = [];
    const threshold = 0.01; // ~1km in degrees (rough approximation)

    this.bridgeLayer.eachLayer(layer => {
      if (layer.bridgeData && layer.bridgeData.latitude && layer.bridgeData.longitude) {
        const bridgePoint = [layer.bridgeData.latitude, layer.bridgeData.longitude];

        // Check distance to each route segment
        for (let i = 0; i < routeCoordinates.length - 1; i++) {
          const segmentStart = [routeCoordinates[i][1], routeCoordinates[i][0]]; // lat, lng
          const segmentEnd = [routeCoordinates[i + 1][1], routeCoordinates[i + 1][0]]; // lat, lng

          const distance = this.pointToLineDistance(bridgePoint, segmentStart, segmentEnd);

          if (distance < threshold) {
            intersectingBridges.push(layer.bridgeData);
            // Highlight intersecting bridge
            layer.setIcon(L.divIcon({
              className: 'bridge-marker bridge-marker-highlight',
              html: `<div class="bridge-icon highlight">🌉</div>`,
              iconSize: [35, 35],
              iconAnchor: [17, 17]
            }));
            break;
          }
        }
      }
    });

    // Send intersection data to LiveView
    this.pushEvent('route_intersections', {
      intersecting_bridges: intersectingBridges.map(bridge => bridge.id)
    });
  },

  pointToLineDistance(point, lineStart, lineEnd) {
    // Calculate distance from point to line segment
    const [px, py] = point;
    const [x1, y1] = lineStart;
    const [x2, y2] = lineEnd;

    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;

    if (lenSq === 0) return Math.sqrt(A * A + B * B);

    let param = dot / lenSq;

    let xx, yy;

    if (param < 0) {
      xx = x1;
      yy = y1;
    } else if (param > 1) {
      xx = x2;
      yy = y2;
    } else {
      xx = x1 + param * C;
      yy = y1 + param * D;
    }

    const dx = px - xx;
    const dy = py - yy;

    return Math.sqrt(dx * dx + dy * dy);
  }
};

export default MapHook;
