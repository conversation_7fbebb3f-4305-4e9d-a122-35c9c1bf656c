# Starlight Phoenix Development Container

A minimal GitHub Codespaces / VS Code Dev Container configuration for Phoenix development.

## What's Included

- **Elixir 1.14** with OTP 26
- **PostgreSQL 15** database
- **Node.js 18** for asset compilation
- **Essential VS Code extensions**:
  - <PERSON><PERSON>rLS (Elixir language support)
  - Phoenix (LiveView syntax highlighting)
  - Augment Code (AI assistance)

## Quick Start

1. **GitHub Codespaces**: Click "Code" → "Create codespace on main"
2. **VS Code**: Use "Dev Containers: Reopen in Container"
3. **Setup**: Run the following commands after container starts:
   ```bash
   mix setup
   mix phx.server
   ```
4. **Access**: Visit http://localhost:4000

## Manual Setup Required

After the container starts, you'll need to run:

```bash
# Install dependencies and setup database
mix setup

# Start the Phoenix server
mix phx.server
```

## Port Forwarding

- **4000**: Phoenix development server

## Database

PostgreSQL is available with default credentials:
- Host: localhost
- Port: 5432
- User: postgres
- Password: postgres

You'll need to create the databases manually:
```bash
sudo service postgresql start
sudo -u postgres createdb starlight_dev
sudo -u postgres createdb starlight_test
```
