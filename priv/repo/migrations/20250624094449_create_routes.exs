defmodule Starlight.Repo.Migrations.CreateRoutes do
  use Ecto.Migration

  def change do
    create table(:routes, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :name, :string, null: false
      add :description, :text

      # Route geometry - PostGIS LINESTRING when available
      # add :geometry, :geometry, null: false  # PostGIS LINESTRING - disabled until PostGIS is available
      add :coordinates, :map, null: false  # Fallback: GeoJSON-like structure

      # Route properties
      add :total_length, :decimal  # Total route length in meters
      add :start_location, :string  # Human-readable start location
      add :end_location, :string    # Human-readable end location

      # Analysis status
      add :analysis_status, :string, default: "pending"  # "pending", "analyzing", "completed", "error"
      add :last_analyzed_at, :utc_datetime

      # Metadata
      add :created_by, references(:users, on_delete: :nilify_all)

      timestamps(type: :utc_datetime)
    end

    create index(:routes, [:name])
    create index(:routes, [:analysis_status])
    create index(:routes, [:created_by])
    create index(:routes, [:inserted_at])

    # PostGIS spatial index - will be created when PostGIS is available
    # create index(:routes, [:geometry], using: :gist)
  end
end
