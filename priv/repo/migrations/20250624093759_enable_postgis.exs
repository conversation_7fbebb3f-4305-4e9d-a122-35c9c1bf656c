defmodule Starlight.Repo.Migrations.EnablePostgis do
  use Ecto.Migration

  def up do
    # Enable PostGIS extension - commented out until PostGIS is installed
    # execute("CREATE EXTENSION IF NOT EXISTS postgis")
    # execute("CREATE EXTENSION IF NOT EXISTS postgis_topology")
    :ok
  end

  def down do
    # Disable PostGIS extensions (be careful with this in production)
    # execute("DROP EXTENSION IF EXISTS postgis_topology")
    # execute("DROP EXTENSION IF EXISTS postgis")
    :ok
  end
end
