defmodule Starlight.Repo.Migrations.CreateBridges do
  use Ecto.Migration

  def change do
    create table(:bridges, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :name, :string, null: false
      add :description, :text

      # Location - will use PostGIS POINT when available, fallback to lat/lng
      # add :location, :geometry, null: false  # PostGIS POINT - disabled until PostGIS is available
      add :latitude, :decimal, precision: 10, scale: 6, null: false  # Fallback
      add :longitude, :decimal, precision: 10, scale: 6, null: false  # Fallback

      # Structural properties
      add :span_lengths, {:array, :decimal}, null: false  # Array of span lengths in meters
      add :support_conditions, {:array, :string}, null: false  # Array of support types: "fixed", "pinned", "roller"

      # Material properties
      add :elastic_modulus, :decimal, null: false  # E in GPa
      add :moment_of_inertia, {:array, :decimal}, null: false  # I in m^4 for each span

      # Load ratings and operational data
      add :load_rating, :decimal  # Maximum load rating in kN
      add :year_built, :integer
      add :last_inspection, :date
      add :condition_rating, :string  # "excellent", "good", "fair", "poor"

      # Metadata
      add :created_by, references(:users, on_delete: :nilify_all)

      timestamps(type: :utc_datetime)
    end

    create index(:bridges, [:name])
    create index(:bridges, [:condition_rating])
    create index(:bridges, [:created_by])

    # PostGIS spatial index - will be created when PostGIS is available
    # create index(:bridges, [:location], using: :gist)
  end
end
