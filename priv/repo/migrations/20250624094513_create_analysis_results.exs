defmodule Starlight.Repo.Migrations.CreateAnalysisResults do
  use Ecto.Migration

  def change do
    create table(:analysis_results, primary_key: false) do
      add :id, :binary_id, primary_key: true

      # Foreign key relationships
      add :bridge_id, references(:bridges, on_delete: :delete_all, type: :binary_id), null: false
      add :vehicle_id, references(:vehicles, on_delete: :delete_all, type: :binary_id), null: false
      add :route_id, references(:routes, on_delete: :delete_all, type: :binary_id), null: false

      # Analysis results from three moment equation
      add :moment_values, :map, null: false  # JSONB storing moment calculation results
      add :reaction_forces, :map, null: false  # JSONB storing reaction force results
      add :shear_forces, :map, null: false  # JSONB storing shear force results
      add :deflection_values, :map  # JSONB storing deflection calculations

      # Stress calculations and safety analysis
      add :stress_calculations, :map  # JSONB storing stress analysis results
      add :max_moment, :decimal  # Maximum moment value in N⋅m
      add :max_stress, :decimal  # Maximum stress value in Pa
      add :max_deflection, :decimal  # Maximum deflection in meters

      # Pass/fail analysis
      add :pass_fail_status, :boolean, null: false  # Overall pass/fail result
      add :safety_factor_moment, :decimal  # Safety factor for moment
      add :safety_factor_stress, :decimal  # Safety factor for stress
      add :safety_factor_deflection, :decimal  # Safety factor for deflection
      add :overall_safety_factor, :decimal  # Minimum safety factor

      # Analysis metadata
      add :analysis_method, :string, default: "three_moment"  # Analysis method used
      add :calculation_notes, :text  # Any notes or warnings from calculation
      add :calculated_at, :utc_datetime, null: false
      add :calculated_by, references(:users, on_delete: :nilify_all)

      timestamps(type: :utc_datetime)
    end

    create index(:analysis_results, [:bridge_id])
    create index(:analysis_results, [:vehicle_id])
    create index(:analysis_results, [:route_id])
    create index(:analysis_results, [:pass_fail_status])
    create index(:analysis_results, [:calculated_at])
    create index(:analysis_results, [:calculated_by])

    # Composite index for finding results by bridge-vehicle-route combination
    create unique_index(:analysis_results, [:bridge_id, :vehicle_id, :route_id, :calculated_at])
  end
end
