defmodule Starlight.Repo.Migrations.CreateVehicles do
  use Ecto.Migration

  def change do
    create table(:vehicles, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :name, :string, null: false
      add :description, :text

      # Vehicle specifications
      add :gross_vehicle_weight, :decimal, null: false  # Total weight in kN
      add :axle_loads, {:array, :decimal}, null: false  # Individual axle loads in kN
      add :wheelbase_dimensions, {:array, :decimal}, null: false  # Distances between axles in meters
      add :vehicle_classification, :string, null: false  # e.g., "B-Double", "Road Train", "Single Unit"

      # Additional vehicle properties
      add :vehicle_length, :decimal  # Total length in meters
      add :vehicle_width, :decimal   # Width in meters
      add :vehicle_height, :decimal  # Height in meters

      # Regulatory information
      add :permit_required, :boolean, default: false
      add :route_restrictions, {:array, :string}  # Array of restriction codes

      # Metadata
      add :is_standard_vehicle, :boolean, default: false  # True for standard design vehicles
      add :created_by, references(:users, on_delete: :nilify_all)

      timestamps(type: :utc_datetime)
    end

    create index(:vehicles, [:name])
    create index(:vehicles, [:vehicle_classification])
    create index(:vehicles, [:is_standard_vehicle])
    create index(:vehicles, [:created_by])
  end
end
